# Spring MUD Configuration

# Tick System Configuration
# The tick system drives autonomous NPC behavior, regeneration, and time-based events
mud:
  tick:
    # Tick interval in milliseconds (default: 1000ms = 1 second)
    # Lower values = more responsive but higher CPU usage
    # Higher values = less responsive but better performance
    interval: 1000
    # Whether the tick system is enabled (default: true)
    # Set to false to disable all autonomous behavior for testing
    enabled: true

# Spring Configuration
spring:
  # Disable web environment for console application
  main:
    web-application-type: none

# Logging configuration
# Default levels provide minimal noise while showing important game events
# Use 'set debug on' in-game to enable detailed debugging output
logging:
  level:
    # Root logger at WARN to minimize framework noise
    root: WARN
    # MUD packages at INFO to show important game events
    com.terheyden.mud: INFO
  pattern:
    # Clean, readable console output with timestamp and logger name
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Actuator endpoints (for monitoring)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
