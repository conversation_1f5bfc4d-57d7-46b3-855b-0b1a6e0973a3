package com.terheyden.mud.engine.console

import com.terheyden.mud.corelib.living.Player

/**
 * Service for formatting text with colors and handling player color preferences.
 */
object ColorFormatter {

    /**
     * Format text with color decorations based on player preferences.
     * Supports markdown-style decorations:
     * - _text_ -> bold yellow
     * - *text* -> bold white
     * - `text` -> bold blue
     */
    fun formatText(player: Player, text: String): String {
        return if (player.colorEnabled) {
            applyColorDecorations(text)
        } else {
            stripDecorations(text)
        }
    }

    /**
     * Apply color decorations to text using markdown-style syntax.
     */
    private fun applyColorDecorations(text: String): String {
        var result = text

        // Replace _text_ with bold yellow
        result = result.replace("_([^_]+)_".toRegex()) { matchResult ->
            AnsiColors.boldYellow(matchResult.groupValues[1])
        }

        // Replace *text* with bold white
        result = result.replace("\\*([^*]+)\\*".toRegex()) { matchResult ->
            AnsiColors.boldWhite(matchResult.groupValues[1])
        }

        // Replace `text` with bold blue
        result = result.replace("`([^`]+)`".toRegex()) { matchResult ->
            AnsiColors.boldCyan(matchResult.groupValues[1])
        }

        return result
    }

    /**
     * Strip markdown decorations from text without applying colors.
     */
    private fun stripDecorations(text: String): String {
        var result = text

        // Remove _text_ decorations
        result = result.replace("_([^_]+)_".toRegex()) { matchResult ->
            matchResult.groupValues[1]
        }

        // Remove *text* decorations
        result = result.replace("\\*([^*]+)\\*".toRegex()) { matchResult ->
            matchResult.groupValues[1]
        }

        // Remove `text` decorations
        result = result.replace("`([^`]+)`".toRegex()) { matchResult ->
            matchResult.groupValues[1]
        }

        return result
    }

    /**
     * Format a command result message for display to the player.
     */
    fun formatCommandResult(message: String, player: Player): String {
        return formatText(player, message)
    }

    /**
     * Format the game prompt for display.
     */
    fun formatPrompt(player: Player): String {
        return if (player.colorEnabled) {
            AnsiColors.colorize("> ", AnsiColors.BOLD_GREEN)
        } else {
            "> "
        }
    }
}
