package com.terheyden.mud.engine.config

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties
data class EngineProperties(
    var mud: MudProperties = MudProperties(),
    var logging: LoggingProperties = LoggingProperties(),
) {

    data class MudProperties(
        var tick: TickProperties = TickProperties(),
    ) {
        data class TickProperties(
            var interval: Long = 1000,
            var enabled: Boolean = true,
        )
    }

    data class LoggingProperties(
        var level: LevelProperties = LevelProperties(),
    ) {
        data class LevelProperties(
            var root: String = "WARN",
            var mud: String = "INFO",
        )
    }
}
