package com.terheyden.mud.engine

import com.terheyden.mud.engine.config.EngineProperties
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication

@SpringBootApplication(
    scanBasePackages = ["com.terheyden.mud"],
)
@EnableConfigurationProperties(EngineProperties::class)
class SpringMudApplication

@Suppress("SpreadOperator")
fun main(args: Array<String>) {
    // Disable web environment since this is a console application
    System.setProperty("spring.main.web-application-type", "none")
    runApplication<SpringMudApplication>(*args)
}
