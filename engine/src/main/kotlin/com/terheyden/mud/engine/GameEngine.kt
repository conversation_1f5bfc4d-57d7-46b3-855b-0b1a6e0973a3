package com.terheyden.mud.engine

import com.terheyden.mud.corelib.command.CommandResult
import com.terheyden.mud.corelib.living.Player
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Component

/**
 * The core game engine that processes commands and manages game state.
 */
@Component
class GameEngine(
    private val commandParser: CommandParser,
) {

    private val logger = KotlinLogging.logger {}

    /**
     * Process a user input command.
     * @param input The raw user input
     * @return The result of the command execution
     */
    fun processCommand(input: String): CommandResult {
        logger.debug { "Processing command input: '$input'" }
        val parsedCommand = commandParser.parseCommand(input)

        return if (parsedCommand != null) {
            val (command, args) = parsedCommand
            logger.debug { "Executing command: ${command.name} with args: $args" }
            try {
                val result = command.execute(args)
                logger.debug { "Command ${command.name} completed successfully" }
                result
            } catch (e: Exception) {
                logger.error(e) { "Error executing command ${command.name}" }
                CommandResult("An error occurred while executing the command: ${e.message}")
            }
        } else {
            logger.debug { "Unknown command: '$input'" }
            CommandResult("I don't understand that command. Type 'help' for available commands.")
        }
    }

    fun getGameBanner() = """
        .d88888b                    oo                   8888ba.88ba  dP     dP 888888ba  
        88.    "'                                        88  `8b  `8b 88     88 88    `8b 
        `Y88888b. 88d888b. 88d888b. dP 88d888b. .d8888b. 88   88   88 88     88 88     88 
              `8b 88'  `88 88'  `88 88 88'  `88 88'  `88 88   88   88 88     88 88     88 
        d8'   .8P 88.  .88 88       88 88    88 88.  .88 88   88   88 Y8.   .8P 88    .8P 
         Y88888P  88Y888P' dP       dP dP    dP `8888P88 dP   dP   dP `Y88888P' 8888888P  
                  88                                 .88                                  
                  dP                             d8888P                                   
    """.trimIndent()

    /**
     * Get the initial game state message for a new player.
     */
    fun getWelcomeMessage(player: Player) = buildString {
        appendLine("=== Welcome to *Spring MUD!* ===")
        appendLine()
        appendLine("Welcome, *${player.name}*! You find yourself in a _mystical realm_...")
        appendLine()
        appendLine("Type `help` for available commands.")
        appendLine("Type `look` to examine your surroundings.")
        appendLine("Type `quit` to exit the game.")
        appendLine("Type `set color off` to disable colors.")
        appendLine()
    }
}
