package com.terheyden.mud.engine.tick

import com.terheyden.mud.corelib.GameService.events
import com.terheyden.mud.corelib.tick.TickEvent
import com.terheyden.mud.corelib.tick.TickSystemStartedEvent
import com.terheyden.mud.corelib.tick.TickSystemStoppedEvent
import com.terheyden.mud.engine.config.EngineProperties
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.DisposableBean
import org.springframework.beans.factory.InitializingBean
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * Core tick service that drives the MUD's heartbeat system.
 * This service runs on a fixed schedule and updates all registered tickable objects.
 *
 * ## Design Decisions:
 *
 * **InitializingBean vs @PostConstruct**: Uses Spring's native InitializingBean interface
 * instead of JSR-250 @PostConstruct annotation. This is more future-proof as Spring
 * is moving away from JSR annotations in favor of Spring-native approaches.
 *
 * **@DependsOn**: Ensures TickableRegistrationService initializes first, so all
 * Living entities are registered before the tick system starts processing.
 *
 * **Command-line Application**: Uses InitializingBean.afterPropertiesSet() instead of
 * ApplicationReadyEvent because in command-line apps, ApplicationReadyEvent only fires
 * when the application is shutting down, not when it's ready to process.
 *
 * **@Async on tick()**: Prevents blocking the scheduler thread if tick processing
 * takes longer than expected, ensuring consistent tick intervals.
 */
@Service
@EnableScheduling
class TickScheduler(
    private val engineProperties: EngineProperties,
) : InitializingBean, DisposableBean {

    private val logger = KotlinLogging.logger {}
    private val isRunning = AtomicBoolean(false)
    private val tickCount = AtomicLong(0)
    private var lastTickTime = System.currentTimeMillis()

    /**
     * Spring InitializingBean callback - called after all properties are set.
     * This is the modern Spring-native way to handle post-construction initialization.
     */
    override fun afterPropertiesSet() {
        if (engineProperties.mud.tick.enabled) {
            start()
        } else {
            logger.info { "Tick system is disabled by configuration." }
        }
    }

    /**
     * Start the tick system.
     */
    fun start() {
        if (isRunning.compareAndSet(false, true)) {
            lastTickTime = System.currentTimeMillis()
            tickCount.set(0)
            events.publish(TickSystemStartedEvent())
            logger.info { "Tick system started with interval: ${engineProperties.mud.tick.interval}ms" }
        }
    }

    /**
     * Stop the tick system.
     */
    fun stop() {
        if (isRunning.compareAndSet(true, false)) {
            val totalTicks = tickCount.get()
            events.publish(TickSystemStoppedEvent(totalTicks = totalTicks))
            logger.info { "Tick system stopped after $totalTicks ticks." }
        }
    }

    /**
     * Spring DisposableBean callback - called when the bean is being destroyed.
     * Ensures the tick system is properly stopped when the application shuts down.
     */
    override fun destroy() {
        logger.info { "TickScheduler is being destroyed, stopping tick system..." }
        stop()
    }

    /**
     * Main tick method - called on schedule.
     */
    @Async
    @Scheduled(fixedRateString = "\${mud.tick.interval:1000}")
    fun tick() {
        if (!isRunning.get()) return

        val currentTime = System.currentTimeMillis()
        val deltaTime = currentTime - lastTickTime
        val currentTick = tickCount.incrementAndGet()

        try {
            // Publish tick event
            events.publish(TickEvent(currentTick, deltaTime))

        } catch (e: Exception) {
            logger.error(e) { "Error during tick processing." }
        } finally {
            lastTickTime = currentTime
        }
    }

    /**
     * Get current tick count.
     */
    fun getCurrentTick(): Long = tickCount.get()

    /**
     * Check if the tick system is running.
     */
    fun isRunning(): Boolean = isRunning.get()

    /**
     * Get tick statistics.
     */
    fun getTickStats(): TickStats {
        return TickStats(
            currentTick = tickCount.get(),
            isRunning = isRunning.get(),
            intervalMs = engineProperties.mud.tick.interval,
        )
    }
}

/**
 * Statistics about the tick system.
 */
data class TickStats(
    val currentTick: Long,
    val isRunning: Boolean,
    val intervalMs: Long,
)
