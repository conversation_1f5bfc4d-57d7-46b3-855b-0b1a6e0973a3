package com.terheyden.mud.engine

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.command.Command
import org.springframework.stereotype.Component

/**
 * Parses user input into commands and arguments.
 */
@Component
class CommandParser(
    private val commands: List<Command>,
) {
    /**
     * Parse user input and find the matching command.
     * @param input The raw user input
     * @return A pair of (Command, arguments) or null if no command found
     */
    fun parseCommand(input: String): Pair<Command, List<String>>? {
        val trimmedInput = input.trim()
        if (trimmedInput.isEmpty()) {
            return null
        }

        val parts = trimmedInput.split("\\s+".toRegex())
        val commandName = parts[0].lowercase()
        val args = parts.drop(1)

        // If there's a special overriding hook input command, use that.
        GameService.inputHookCommand?.let {
            return Pair(it, parts)
        }

        // Try to find a command by name or alias
        val command = commands.find { cmd ->
            cmd.name.equals(commandName, ignoreCase = true) ||
                    cmd.aliases.any { it.equals(commandName, ignoreCase = true) }
        }

        if (command != null) {
            return Pair(command, args)
        }

        // If no command found, check if it's a direction for movement
        val direction = Direction.fromString(commandName)
        if (direction != null) {
            val moveCommand = commands.find { it.name == "move" }
            if (moveCommand != null) {
                // For direction commands, pass the direction as an argument
                return Pair(moveCommand, listOf(commandName))
            }
        }

        return null
    }
}
