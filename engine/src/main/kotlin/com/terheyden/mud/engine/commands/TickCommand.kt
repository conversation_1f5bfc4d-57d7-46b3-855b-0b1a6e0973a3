package com.terheyden.mud.engine.commands

import com.terheyden.mud.corelib.command.Command
import com.terheyden.mud.corelib.command.CommandResult
import com.terheyden.mud.engine.tick.TickScheduler
import org.springframework.stereotype.Component

/**
 * Command to view tick system status and statistics.
 * Useful for debugging and monitoring the heartbeat system.
 */
@Component
@Suppress("MagicNumber")
class TickCommand(
    private val tickScheduler: TickScheduler,
) : Command {

    override val name = "tick"
    override val aliases = listOf("heartbeat", "tickstats", "tickinfo")
    override val description = "View tick system status and statistics"

    override fun execute(args: List<String>): CommandResult {
        val stats = tickScheduler.getTickStats()

        val message = buildString {
            appendLine("=== Tick System Status ===")
            appendLine()
            appendLine("Status: ${if (stats.isRunning) "*Running*" else "_Stopped_"}")
            appendLine("Current Tick: `${stats.currentTick}`")
            appendLine("Tick Interval: `${stats.intervalMs}ms`")
            appendLine()

            if (stats.isRunning) {
                val uptimeSeconds = (stats.currentTick * stats.intervalMs) / 1000
                val uptimeMinutes = uptimeSeconds / 60
                val uptimeHours = uptimeMinutes / 60

                appendLine("Uptime: `${uptimeHours}h ${uptimeMinutes % 60}m ${uptimeSeconds % 60}s`")
                appendLine("Average TPS: `${1000.0 / stats.intervalMs}` ticks per second")
            } else {
                appendLine("The tick system is currently stopped.")
            }

            appendLine()
            appendLine("The tick system drives autonomous NPC behavior, regeneration,")
            appendLine("and other time-based game mechanics.")
        }

        return CommandResult(message)
    }
}
