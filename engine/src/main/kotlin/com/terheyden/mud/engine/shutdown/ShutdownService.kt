package com.terheyden.mud.engine.shutdown

import com.terheyden.mud.engine.tick.TickScheduler
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.DisposableBean
import org.springframework.stereotype.Service

/**
 * Service responsible for coordinating graceful shutdown of all game systems.
 *
 * ## Design Decisions:
 *
 * **Coordinated Shutdown**: Ensures all game systems are stopped in the correct
 * order when the application shuts down, preventing hanging processes or
 * incomplete cleanup.
 *
 * **DisposableBean**: Uses Spring's lifecycle interface to hook into the
 * application shutdown process and perform cleanup operations.
 */
@Service
class ShutdownService(
    private val tickScheduler: TickScheduler,
) : DisposableBean {

    private val logger = KotlinLogging.logger {}

    /**
     * Spring DisposableBean callback - called when the application is shutting down.
     * Coordinates the shutdown of all game systems in the proper order.
     */
    override fun destroy() {
        logger.info { "ShutdownService: Beginning graceful shutdown..." }

        try {
            // Stop the tick system first to prevent new autonomous actions
            logger.info { "Stopping tick system..." }
            tickScheduler.stop()

            // Additional cleanup can be added here for other systems
            // e.g., save player data, close database connections, etc.

            logger.info { "Graceful shutdown completed successfully" }
        } catch (e: Exception) {
            logger.error(e) { "Error during graceful shutdown" }
        }
    }

    /**
     * Manually trigger shutdown (for testing or emergency shutdown).
     */
    fun shutdown() {
        logger.info { "Manual shutdown requested" }
        destroy()
    }
}
