package com.terheyden.mud.engine

import com.terheyden.mud.corelib.GameWorld
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * A Spring Boot test to verify the game world is properly initialized.
 */
@SpringBootTest
@ActiveProfiles("test")
class SpringMudApplicationTest {

    @Autowired
    private lateinit var gameWorld: GameWorld

    @Test
    fun `start room should have exits after initialization`() {
        // Get the starting room
        val startRoom = gameWorld.getStartingRoom()
        assertNotNull(startRoom, "Start room should exist")

        // Check that it has exits
        val availableExits = startRoom.getAvailableExits()
        assertTrue(availableExits.isNotEmpty(), "Start room should have exits")
    }
}
