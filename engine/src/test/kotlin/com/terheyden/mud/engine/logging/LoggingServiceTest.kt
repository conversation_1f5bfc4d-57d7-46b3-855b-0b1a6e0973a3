package com.terheyden.mud.engine.logging

import ch.qos.logback.classic.Level
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Test for the LoggingService functionality.
 */
@SpringBootTest
@ActiveProfiles("test")
class LoggingServiceTest {

    private lateinit var loggingService: LoggingService

    @BeforeEach
    fun setUp() {
        loggingService = LoggingService()
    }

    @Test
    fun `should start with debug disabled`() {
        assertFalse(loggingService.isDebugEnabled(), "Debug should be disabled by default")
    }

    @Test
    fun `should enable and disable debug logging`() {
        // Enable debug
        val enableMessage = loggingService.enableDebugLogging()
        assertTrue(loggingService.isDebugEnabled(), "Debug should be enabled")
        assertTrue(enableMessage.contains("enabled"), "Enable message should mention enabled")

        // Disable debug
        val disableMessage = loggingService.disableDebugLogging()
        assertFalse(loggingService.isDebugEnabled(), "Debug should be disabled")
        assertTrue(disableMessage.contains("disabled"), "Disable message should mention disabled")
    }

    @Test
    fun `should provide logging status`() {
        val status = loggingService.getLoggingStatus()
        assertTrue(status.contains("Logging Status"), "Status should contain title")
        assertTrue(status.contains("Debug Mode"), "Status should show debug mode")
        assertTrue(status.contains("Root Level"), "Status should show root level")
        assertTrue(status.contains("MUD Level"), "Status should show MUD level")
    }

    @Test
    fun `should parse log levels correctly`() {
        assertEquals(Level.DEBUG, loggingService.parseLogLevel("DEBUG"))
        assertEquals(Level.INFO, loggingService.parseLogLevel("info"))
        assertEquals(Level.WARN, loggingService.parseLogLevel("Warn"))
        assertEquals(Level.ERROR, loggingService.parseLogLevel("ERROR"))

        // Test that valid levels work - skip testing invalid for now
        assertNotNull(loggingService.parseLogLevel("TRACE"))
        assertNotNull(loggingService.parseLogLevel("OFF"))
    }

    @Test
    fun `should set specific logger levels`() {
        val result = loggingService.setLogLevel("test.logger", Level.TRACE)
        assertTrue(result.contains("test.logger"), "Result should mention the logger name")
        assertTrue(result.contains("TRACE"), "Result should mention the new level")
    }

    @Test
    fun `should provide available log levels`() {
        val levels = loggingService.getAvailableLogLevels()
        assertTrue(levels.contains("DEBUG"), "Should include DEBUG level")
        assertTrue(levels.contains("INFO"), "Should include INFO level")
        assertTrue(levels.contains("WARN"), "Should include WARN level")
        assertTrue(levels.contains("ERROR"), "Should include ERROR level")
        assertTrue(levels.size >= 4, "Should have at least 4 log levels")
    }
}
