package com.terheyden.mud.engine.tick

import com.terheyden.mud.corelib.GameService.tickService
import com.terheyden.mud.corelib.tick.Tickable
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import java.util.concurrent.atomic.AtomicInteger

/**
 * Integration test to verify the tick system works with the full Spring context.
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(
    properties = [
        "mud.tick.enabled=true",
        "mud.tick.interval=100" // Fast ticks for testing
    ]
)
class TickIntegrationTest {

    @Autowired
    lateinit var tickScheduler: TickScheduler

    @Test
    fun testTickSystem() {
        // TickScheduler should be running and publishing ticks.
        // TickService subscribes to the tick event and starts with no Tickables.

        // Register a tickable that counts ticks.
        val counter = AtomicInteger(0)
        val testTickable = object : Tickable {
            override fun onTick(tickCount: Long) {
                counter.incrementAndGet()
            }
        }
        tickService.register(testTickable)

        // Wait for a few ticks to pass.
        Thread.sleep(250)

        // Verify the tickable was called.
        assertThat(counter.get()).isGreaterThan(0)

        // Unregister ourselves.
        tickService.unregister(testTickable)
        val prevCount = counter.get()

        // Wait for a few more ticks to pass.
        Thread.sleep(250)

        // Verify the tickable was not called again.
        assertThat(counter.get()).isEqualTo(prevCount)
    }
}
