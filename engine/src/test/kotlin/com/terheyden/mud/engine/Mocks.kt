package com.terheyden.mud.engine

import com.terheyden.mud.engine.config.EngineProperties

object Mocks {

    fun createEngineProperties(
        tickInterval: Long = 1000,
        tickEnabled: Boolean = true,
        rootLogLevel: String = "WARN",
        mudLogLevel: String = "INFO",
    ) = EngineProperties(
        mud = EngineProperties.MudProperties(
            tick = EngineProperties.MudProperties.TickProperties(
                interval = tickInterval,
                enabled = tickEnabled,
            ),
        ),
        logging = EngineProperties.LoggingProperties(
            level = EngineProperties.LoggingProperties.LevelProperties(
                root = rootLogLevel,
                mud = mudLogLevel,
            ),
        ),
    )
}
