#!/bin/bash

echo "=== Testing Interactive Quit Command ==="
echo ""
echo "This test simulates a player actually typing 'quit' and verifies proper shutdown."
echo ""

# Create input that includes the quit command
cat > interactive_quit_input.txt << 'EOF'
Adventurer
look
tick
quit
EOF

echo "Starting game with simulated player input including 'quit' command..."
echo ""

# Run the game with input
timeout 15s ./gradlew bootRun < interactive_quit_input.txt

exit_code=$?

echo ""
if [ $exit_code -eq 0 ]; then
    echo "✅ SUCCESS: Application processed 'quit' command and shut down gracefully!"
elif [ $exit_code -eq 124 ]; then
    echo "❌ FAILURE: Application timed out (still hanging after 15 seconds)"
else
    echo "⚠️  WARNING: Application exited with code $exit_code"
fi

# Clean up
rm -f interactive_quit_input.txt

echo ""
echo "Interactive quit test completed."
