#!/bin/bash

echo "=== Testing Debug Logging System ==="
echo ""
echo "This test will demonstrate the in-game debug logging functionality:"
echo "1. Start with default WARN log level"
echo "2. Use 'set debug on' to enable debug logging"
echo "3. Use 'set debug status' to check logging status"
echo "4. Use 'set debug off' to disable debug logging"
echo "5. Use 'set log com.terheyden.mud DEBUG' for specific logger control"
echo ""

# Create input commands for the game
cat > debug_test_input.txt << 'EOF'
set
set debug status
set debug on
tick
set debug status
north
look
set debug off
set debug status
set log com.terheyden.mud.engine.tick DEBUG
tick
quit
EOF

echo "Starting game with debug logging test commands..."
echo ""

# Run the game with input
timeout 30s ./gradlew bootRun < debug_test_input.txt

echo ""
echo "Debug logging test completed!"
echo ""
echo "You should have seen:"
echo "  - Initial settings showing debug OFF"
echo "  - Debug logging enabled message"
echo "  - Detailed tick system debug output"
echo "  - Debug logging disabled message"
echo "  - Specific logger level changes"
