# Spring MUD Architecture

## Overview

This document describes the key architectural decisions and design patterns used in the Spring MUD project.

## Core Systems

### 1. Tick System (Heartbeat)

**Purpose**: Drives autonomous NPC behavior, regeneration, and time-based game events.

**Key Components**:

- `TickService`: Core scheduler that processes ticks every 2 seconds
- `TickRegistry`: Thread-safe registry of all tickable objects
- `Tickable`: Interface for objects that need periodic updates
- `TickableRegistrationService`: Auto-discovers and registers Living entities

**Design Decisions**:

- **InitializingBean vs @PostConstruct**: Uses Spring's native `InitializingBean` interface instead of JSR-250
  `@PostConstruct` annotation for future-proofing
- **@DependsOn**: Ensures proper initialization order (registration before tick processing)
- **Command-line Compatibility**: Uses `afterPropertiesSet()` instead of `ApplicationReadyEvent` because the latter only
  fires on shutdown in console apps
- **@Async Processing**: Prevents blocking the scheduler thread if tick processing takes too long

### 2. Dynamic Logging System

**Purpose**: Provides runtime control over log levels for debugging and monitoring.

**Key Components**:

- `LoggingService`: Manages Logback logger levels dynamically
- `SetCommand`: In-game interface for log level control
- Configuration in `application.yml`

**Design Decisions**:

- **Runtime Control**: Uses Logback's `LoggerContext` for live log level changes
- **Sensible Defaults**: WARN for root, INFO for MUD packages to minimize noise
- **Package Separation**: Separates MUD logging from framework logging
- **In-game Integration**: Seamless integration with game commands for real-time debugging

### 3. Autonomous NPC Behavior

**Purpose**: Makes NPCs feel alive through random speech and actions.

**Key Components**:

- `NPC.onTickBehavior()`: Base autonomous behavior framework
- `VillageElder`: Example implementation with random speech/actions
- Room message system for NPC communication

**Design Decisions**:

- **Probability-based**: NPCs use random chance to avoid spam
- **Type-specific Behavior**: Different NPC types (aggressive, friendly, neutral) have different patterns
- **Configurable Intervals**: Different NPCs can tick at different frequencies for performance
- **Room Message System**: Persistent messages that players see when entering/looking

## Spring Boot Patterns

### Dependency Injection

- **Constructor Injection**: Preferred over field injection for testability
- **List Injection**: Automatic discovery of all beans of a type (e.g., `List<Living>`)
- **ConfigurationProperty Objects**: Modern Kotlin-style configuration injection with sensible defaults

### Lifecycle Management

- **InitializingBean**: Modern Spring-native lifecycle management
- **@DependsOn**: Explicit dependency ordering when needed
- **@Service/@Component**: Clear separation of concerns

### Configuration

- **application.yml**: Centralized configuration with documentation
- **@ConfigurationProperties**: Type-safe configuration binding
- **Profile Support**: Ready for different environments (dev, prod, test)

## Performance Considerations

### Tick System

- **Configurable Intervals**: Different entities can tick at different rates
- **Async Processing**: Non-blocking tick execution
- **Selective Processing**: Only active/enabled tickables are processed
- **Error Isolation**: Individual tickable failures don't crash the system

### Logging

- **Lazy Evaluation**: Kotlin logging with lambda expressions
- **Level-based Filtering**: Expensive debug operations only when needed
- **Minimal Default Output**: WARN level reduces I/O overhead

### Memory Management

- **Limited Message History**: Room messages are capped at 5 entries
- **Weak References**: Where appropriate to prevent memory leaks
- **Efficient Collections**: ConcurrentHashMap for thread-safe registries

## Testing Strategy

### Unit Tests

- **Service Layer**: Comprehensive testing of business logic
- **Mock Dependencies**: Isolated testing with MockK
- **Spring Boot Test**: Integration testing with full context

### Integration Tests

- **Tick System**: End-to-end tick processing verification
- **Command Processing**: Full command execution pipeline testing
- **Logging**: Runtime log level changes verification

## Future Extensibility

### Planned Enhancements

- **Multi-player Support**: Room message broadcasting
- **Quest System**: Time-based quest events via tick system
- **Weather/Day-Night**: Environmental systems using ticks
- **Performance Monitoring**: Metrics collection and reporting

### Extension Points

- **Tickable Interface**: Easy to add new time-based behaviors
- **Command System**: Pluggable command architecture
- **Event System**: Decoupled communication between systems
- **Configuration**: Externalized settings for easy customization

## Best Practices

### Code Organization

- **Package by Feature**: Related classes grouped together
- **Clear Interfaces**: Well-defined contracts between components
- **Separation of Concerns**: Each class has a single responsibility

### Documentation

- **Inline Comments**: Explain design decisions and trade-offs
- **KDoc**: Comprehensive API documentation
- **Architecture Docs**: High-level system design documentation

### Error Handling

- **Graceful Degradation**: System continues operating when components fail
- **Comprehensive Logging**: Detailed error information for debugging
- **Input Validation**: Robust handling of invalid user input
