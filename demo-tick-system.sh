#!/bin/bash

echo "=== Spring MUD Tick System Demo ==="
echo ""
echo "Starting the game with tick system enabled..."
echo "The tick system will:"
echo "  - Run every 2 seconds (configurable)"
echo "  - Regenerate player health automatically"
echo "  - Enable NPC autonomous behavior"
echo "  - Process time-based game events"
echo ""
echo "Try these commands to see the tick system in action:"
echo "  'tick' - View tick system status"
echo "  'status' - View your health (watch it regenerate over time)"
echo "  'attack wolf' - Damage yourself, then watch health regenerate"
echo "  'look' - See NPCs in rooms (they have autonomous behavior)"
echo ""
echo "Press Ctrl+C to stop the demo"
echo ""

# Start the game
./gradlew bootRun
