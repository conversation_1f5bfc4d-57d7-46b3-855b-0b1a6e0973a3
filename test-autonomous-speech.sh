#!/bin/bash

echo "=== Testing Village Elder Autonomous Speech ==="
echo ""
echo "This test will:"
echo "1. Start the game"
echo "2. Navigate to the Village Square (where the Village Elder is)"
echo "3. Wait and observe the elder's autonomous behavior"
echo "4. Use 'look' command to see recent room messages"
echo ""
echo "The Village Elder should randomly:"
echo "  - Say wise things every ~30 seconds (15 ticks)"
echo "  - Perform actions every ~50 seconds (25 ticks)"
echo ""
echo "Commands to try:"
echo "  'north' - Go to Village Square"
echo "  'look' - See room and any recent NPC activity"
echo "  'tick' - Check tick system status"
echo "  'say hello' - Talk to the elder"
echo ""

# Create input commands for the game
cat > game_input.txt << 'EOF'
north
look
tick
say hello elder
look
EOF

echo "Starting game with pre-programmed commands..."
echo "Watch for the Village Elder's autonomous speech and actions!"
echo ""

# Run the game with input
timeout 60s ./gradlew bootRun < game_input.txt

echo ""
echo "Demo completed! The Village Elder should have shown autonomous behavior."
echo "In a real game session, you would see the elder randomly speak and act over time."
