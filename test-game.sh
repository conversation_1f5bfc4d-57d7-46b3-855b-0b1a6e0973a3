#!/bin/bash

# Test script to verify the game is working properly
echo "Testing Spring MUD modular architecture..."
echo "=========================================="

# Build the project
echo "Building project..."
./gradlew build -q

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"

# Run the exit test
echo "Running exit test..."
./gradlew :engine:test --tests ExitTest -q

if [ $? -ne 0 ]; then
    echo "❌ Exit test failed!"
    exit 1
fi

echo "✅ Exit test passed!"

echo ""
echo "🎉 All tests passed! The modular architecture is working correctly."
echo ""
echo "To run the game interactively:"
echo "  ./run-game.sh"
echo ""
echo "Or directly:"
echo "  java -jar engine/build/libs/engine-0.0.1-SNAPSHOT.jar"
echo ""
echo "Module structure:"
echo "  📦 corelib  - Core interfaces and base classes"
echo "  🎮 engine   - Spring Boot application and game engine"  
echo "  🌍 mudlib   - Default game world content"
