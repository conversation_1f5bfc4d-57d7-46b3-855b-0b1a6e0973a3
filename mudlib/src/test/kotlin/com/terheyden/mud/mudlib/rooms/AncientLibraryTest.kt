package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.puzzle.PuzzleState
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AncientLibraryTest {

    private lateinit var library: AncientLibrary

    @BeforeEach
    fun setUp() {
        library = AncientLibrary()
    }

    @Test
    fun `should start with unsolved puzzle state`() {
        assertThat(library.puzzleState).isEqualTo(PuzzleState.UNSOLVED)
        assertThat(library.isSolved()).isFalse
    }

    @Test
    fun `should handle riddle answers through puzzle interaction`() {
        val result = library.handlePuzzleInteraction("player1", "say", "map")
        assertThat(result).isNotNull
        assertThat(result).contains("Correct!")
        assertThat(library.puzzleState).isEqualTo(PuzzleState.IN_PROGRESS)
    }

    @Test
    fun `should handle wrong answers`() {
        val result = library.handlePuzzleInteraction("player1", "say", "wrong answer")
        assertThat(result).isNotNull
        assertThat(result).contains("not correct")
    }

    @Test
    fun `should complete puzzle after all riddles solved`() {
        // Answer all riddles correctly
        library.handlePuzzleInteraction("player1", "say", "map")
        library.handlePuzzleInteraction("player1", "say", "footsteps")
        val result = library.handlePuzzleInteraction("player1", "say", "echo")

        assertThat(result).contains("Excellent!")
        assertThat(library.puzzleState).isEqualTo(PuzzleState.SOLVED)
        assertThat(library.isSolved()).isTrue
    }

    @Test
    fun `should provide riddle text when examining book`() {
        val result = library.handlePuzzleInteraction("player1", "examine", "book")
        assertThat(result).isNotNull
        assertThat(result).contains("riddle")
        assertThat(result).contains("cities")
    }

    @Test
    fun `should handle use book interaction`() {
        val result = library.handlePuzzleInteraction("player1", "use", "book")
        assertThat(result).isNotNull
        assertThat(result).contains("riddle")
    }

    @Test
    fun `should reset puzzle state correctly`() {
        // Progress through some riddles
        library.handlePuzzleInteraction("player1", "say", "map")
        library.handlePuzzleInteraction("player1", "say", "wrong") // This should fail

        library.resetPuzzle()

        assertThat(library.puzzleState).isEqualTo(PuzzleState.UNSOLVED)

        // Should be able to start over
        val result = library.handlePuzzleInteraction("player1", "examine", "book")
        assertThat(result).contains("cities") // First riddle again
    }

    @Test
    fun `should include puzzle state in room description`() {
        val initialDescription = library.getFullDescriptionWithPuzzle()
        assertThat(initialDescription).contains("Ancient Library")

        // Progress puzzle
        library.handlePuzzleInteraction("player1", "say", "map")
        val progressDescription = library.getFullDescriptionWithPuzzle()
        assertThat(progressDescription).contains("responding to your actions")

        // Complete puzzle
        library.handlePuzzleInteraction("player1", "say", "footsteps")
        library.handlePuzzleInteraction("player1", "say", "echo")
        val solvedDescription = library.getFullDescriptionWithPuzzle()
        assertThat(solvedDescription).contains("unlocked wisdom")
    }

    @Test
    fun `should handle case insensitive actions`() {
        val result1 = library.handlePuzzleInteraction("player1", "SAY", "map")
        assertThat(result1).contains("Correct!")

        library.resetPuzzle()

        val result2 = library.handlePuzzleInteraction("player1", "EXAMINE", "book")
        assertThat(result2).contains("riddle")
    }

    @Test
    fun `should return null for unhandled interactions`() {
        val result = library.handlePuzzleInteraction("player1", "dance", "book")
        assertThat(result).isNull()
    }

    @Test
    fun `should handle already solved state`() {
        // Complete the puzzle
        library.handlePuzzleInteraction("player1", "say", "map")
        library.handlePuzzleInteraction("player1", "say", "footsteps")
        library.handlePuzzleInteraction("player1", "say", "echo")

        // Try to answer again
        val result = library.handlePuzzleInteraction("player1", "say", "anything")
        assertThat(result).contains("already been solved")
    }

    @Test
    fun `should have correct room connections`() {
        assertThat(library.exits).containsKey(com.terheyden.mud.corelib.Direction.SOUTH)
        assertThat(library.exits).containsKey(com.terheyden.mud.corelib.Direction.EAST)
    }

    @Test
    fun `should have appropriate room features`() {
        assertThat(library.features.getExaminableThings()).contains("towering shelves")
        assertThat(library.features.getExaminableThings()).contains("glowing book")
        assertThat(library.features.getExaminableThings()).contains("floating candles")
    }
}
