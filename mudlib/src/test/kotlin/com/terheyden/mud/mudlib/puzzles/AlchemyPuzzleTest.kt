package com.terheyden.mud.mudlib.puzzles

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AlchemyPuzzleTest {

    private lateinit var puzzle: AlchemyPuzzle

    @BeforeEach
    fun setUp() {
        puzzle = AlchemyPuzzle()
    }

    @Test
    fun `should start with empty cauldron`() {
        assertThat(puzzle.getCauldronContents()).isEmpty()
        assertThat(puzzle.isRecipeComplete()).isFalse
        assertThat(puzzle.hasMixtureFailed()).isFalse
        assertThat(puzzle.getCurrentStep()).isEqualTo(0)
    }

    @Test
    fun `should accept correct first ingredient`() {
        val result = puzzle.addIngredient("spring_water", 2)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.STEP_CORRECT)
        assertThat(puzzle.getCurrentStep()).isEqualTo(1)
        assertThat(puzzle.getCauldronContents()).containsEntry("spring_water", 2)
    }

    @Test
    fun `should reject wrong ingredient`() {
        val result = puzzle.addIngredient("dragon_scale", 1)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.WRONG_INGREDIENT)
        assertThat(puzzle.hasMixtureFailed()).isTrue
    }

    @Test
    fun `should reject wrong quantity`() {
        val result = puzzle.addIngredient("spring_water", 1) // Should be 2
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.WRONG_QUANTITY)
        assertThat(puzzle.hasMixtureFailed()).isTrue
    }

    @Test
    fun `should complete recipe with correct sequence`() {
        assertThat(puzzle.addIngredient("spring_water", 2)).isEqualTo(AlchemyPuzzle.AlchemyResult.STEP_CORRECT)
        assertThat(puzzle.addIngredient("moonflower", 1)).isEqualTo(AlchemyPuzzle.AlchemyResult.STEP_CORRECT)
        assertThat(puzzle.addIngredient("dragon_scale", 1)).isEqualTo(AlchemyPuzzle.AlchemyResult.STEP_CORRECT)
        assertThat(puzzle.addIngredient("nightshade", 1)).isEqualTo(AlchemyPuzzle.AlchemyResult.STEP_CORRECT)
        val result = puzzle.addIngredient("phoenix_feather", 1)

        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.RECIPE_COMPLETE)
        assertThat(puzzle.isRecipeComplete()).isTrue
    }

    @Test
    fun `should reject invalid ingredient`() {
        val result = puzzle.addIngredient("invalid_ingredient", 1)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.INVALID_INGREDIENT)
    }

    @Test
    fun `should reject invalid quantity`() {
        val result = puzzle.addIngredient("spring_water", 0)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.INVALID_QUANTITY)
    }

    @Test
    fun `should reject insufficient ingredient`() {
        val result = puzzle.addIngredient("spring_water", 10) // Only 5 available
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.INSUFFICIENT_INGREDIENT)
    }

    @Test
    fun `should provide current hint`() {
        val hint = puzzle.getCurrentHint()
        assertThat(hint).isNotNull
        assertThat(hint).contains("spring water")

        puzzle.addIngredient("spring_water", 2)
        val nextHint = puzzle.getCurrentHint()
        assertThat(nextHint).contains("moonflower")
    }

    @Test
    fun `should provide ingredient descriptions`() {
        val description = puzzle.getIngredientDescription("moonflower")
        assertThat(description).isNotNull
        assertThat(description).contains("Moonflower Petals")
        assertThat(description).contains("Available: 3")
    }

    @Test
    fun `should describe cauldron contents`() {
        val emptyDescription = puzzle.getCauldronDescription()
        assertThat(emptyDescription).contains("empty")

        puzzle.addIngredient("spring_water", 2)
        val withContents = puzzle.getCauldronDescription()
        assertThat(withContents).contains("spring water")
    }

    @Test
    fun `should reset puzzle state`() {
        puzzle.addIngredient("spring_water", 2)
        puzzle.addIngredient("dragon_scale", 1) // Wrong ingredient, causes failure

        puzzle.reset()

        assertThat(puzzle.getCauldronContents()).isEmpty()
        assertThat(puzzle.getCurrentStep()).isEqualTo(0)
        assertThat(puzzle.hasMixtureFailed()).isFalse
        assertThat(puzzle.isRecipeComplete()).isFalse
    }

    @Test
    fun `should reject actions when mixture is ruined`() {
        puzzle.addIngredient("dragon_scale", 1) // Wrong first ingredient
        assertThat(puzzle.hasMixtureFailed()).isTrue

        val result = puzzle.addIngredient("spring_water", 2)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.MIXTURE_RUINED)
    }

    @Test
    fun `should reject actions when recipe is complete`() {
        // Complete the recipe
        puzzle.addIngredient("spring_water", 2)
        puzzle.addIngredient("moonflower", 1)
        puzzle.addIngredient("dragon_scale", 1)
        puzzle.addIngredient("nightshade", 1)
        puzzle.addIngredient("phoenix_feather", 1)

        val result = puzzle.addIngredient("spring_water", 1)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.ALREADY_COMPLETE)
    }

    @Test
    fun `should reject too many ingredients`() {
        // Complete the recipe first
        puzzle.addIngredient("spring_water", 2)
        puzzle.addIngredient("moonflower", 1)
        puzzle.addIngredient("dragon_scale", 1)
        puzzle.addIngredient("nightshade", 1)
        puzzle.addIngredient("phoenix_feather", 1)

        puzzle.reset()

        // Now try to add more than the recipe calls for
        puzzle.addIngredient("spring_water", 2)
        puzzle.addIngredient("moonflower", 1)
        puzzle.addIngredient("dragon_scale", 1)
        puzzle.addIngredient("nightshade", 1)
        puzzle.addIngredient("phoenix_feather", 1)

        // This should fail as we're trying to add beyond the recipe
        val result = puzzle.addIngredient("spring_water", 1)
        assertThat(result).isEqualTo(AlchemyPuzzle.AlchemyResult.ALREADY_COMPLETE)
    }

    @Test
    fun `should track total steps correctly`() {
        assertThat(puzzle.getTotalSteps()).isEqualTo(5)
    }

    @Test
    fun `should get all available ingredients`() {
        val ingredients = puzzle.getAvailableIngredients()
        assertThat(ingredients).hasSize(5)
        assertThat(ingredients.keys).containsExactlyInAnyOrder(
            "moonflower", "dragon_scale", "spring_water", "phoenix_feather", "nightshade"
        )
    }
}
