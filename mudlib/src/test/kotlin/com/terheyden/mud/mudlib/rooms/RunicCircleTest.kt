package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.puzzle.PuzzleState
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RunicCircleTest {

    private lateinit var runicCircle: RunicCircle

    @BeforeEach
    fun setUp() {
        runicCircle = RunicCircle()
    }

    @Test
    fun `should start with unsolved puzzle state`() {
        assertThat(runicCircle.puzzleState).isEqualTo(PuzzleState.UNSOLVED)
        assertThat(runicCircle.isSolved()).isFalse
    }

    @Test
    fun `should handle rune activation through puzzle interaction`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "touch", "earth rune")
        assertThat(result).isNotNull
        assertThat(result).contains("earth rune flares to life")
        assertThat(runicCircle.puzzleState).isEqualTo(PuzzleState.IN_PROGRESS)
    }

    @Test
    fun `should handle wrong rune activation`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "activate", "fire rune")
        assertThat(result).isNotNull
        assertThat(result).contains("That was not the correct sequence")
    }

    @Test
    fun `should complete puzzle with correct sequence`() {
        // Activate runes in correct order: earth, water, fire, air, spirit
        runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        runicCircle.handlePuzzleInteraction("player1", "touch", "water")
        runicCircle.handlePuzzleInteraction("player1", "touch", "fire")
        runicCircle.handlePuzzleInteraction("player1", "touch", "air")
        val result = runicCircle.handlePuzzleInteraction("player1", "touch", "spirit")

        assertThat(result).contains("completing the sequence")
        assertThat(runicCircle.puzzleState).isEqualTo(PuzzleState.SOLVED)
        assertThat(runicCircle.isSolved()).isTrue
    }

    @Test
    fun `should handle rune examination`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "examine", "earth rune")
        assertThat(result).isNotNull
        assertThat(result).contains("brown rune")
        assertThat(result).contains("dormant")
    }

    @Test
    fun `should provide symbol hints`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "examine", "symbols")
        assertThat(result).isNotNull
        assertThat(result).contains("foundation")
    }

    @Test
    fun `should handle various activation commands`() {
        val touchResult = runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        assertThat(touchResult).contains("earth rune flares")

        runicCircle.resetPuzzle()

        val activateResult = runicCircle.handlePuzzleInteraction("player1", "activate", "earth")
        assertThat(activateResult).contains("earth rune flares")

        runicCircle.resetPuzzle()

        val useResult = runicCircle.handlePuzzleInteraction("player1", "use", "earth")
        assertThat(useResult).contains("earth rune flares")
    }

    @Test
    fun `should handle invalid rune names`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "touch", "invalid rune")
        assertThat(result).contains("must specify which rune")
        assertThat(result).contains("earth, water, fire, air, and spirit")
    }

    @Test
    fun `should reset puzzle state correctly`() {
        // Progress through some runes
        runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        runicCircle.handlePuzzleInteraction("player1", "touch", "fire") // Wrong sequence

        runicCircle.resetPuzzle()

        assertThat(runicCircle.puzzleState).isEqualTo(PuzzleState.UNSOLVED)

        // Should be able to start over
        val result = runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        assertThat(result).contains("earth rune flares")
    }

    @Test
    fun `should handle already solved state`() {
        // Complete the puzzle
        runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        runicCircle.handlePuzzleInteraction("player1", "touch", "water")
        runicCircle.handlePuzzleInteraction("player1", "touch", "fire")
        runicCircle.handlePuzzleInteraction("player1", "touch", "air")
        runicCircle.handlePuzzleInteraction("player1", "touch", "spirit")

        // Try to activate again
        val result = runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        assertThat(result).contains("already been activated")
    }

    @Test
    fun `should extract rune names from various inputs`() {
        val earthResult = runicCircle.handlePuzzleInteraction("player1", "touch", "earth rune")
        assertThat(earthResult).contains("earth rune flares")

        runicCircle.resetPuzzle()

        val waterResult = runicCircle.handlePuzzleInteraction("player1", "touch", "water rune")
        assertThat(waterResult).contains("That was not the correct sequence") // Wrong first rune

        runicCircle.resetPuzzle()

        val fireResult = runicCircle.handlePuzzleInteraction("player1", "touch", "fire rune")
        assertThat(fireResult).contains("That was not the correct sequence") // Wrong first rune
    }

    @Test
    fun `should include puzzle state in room description`() {
        val initialDescription = runicCircle.getFullDescriptionWithPuzzle()
        assertThat(initialDescription).contains("Runic Circle")

        // Progress puzzle
        runicCircle.handlePuzzleInteraction("player1", "touch", "earth")
        val progressDescription = runicCircle.getFullDescriptionWithPuzzle()
        assertThat(progressDescription).contains("responding to your actions")

        // Complete puzzle
        runicCircle.handlePuzzleInteraction("player1", "touch", "water")
        runicCircle.handlePuzzleInteraction("player1", "touch", "fire")
        runicCircle.handlePuzzleInteraction("player1", "touch", "air")
        runicCircle.handlePuzzleInteraction("player1", "touch", "spirit")
        val solvedDescription = runicCircle.getFullDescriptionWithPuzzle()
        assertThat(solvedDescription).contains("elemental power")
    }

    @Test
    fun `should return null for unhandled interactions`() {
        val result = runicCircle.handlePuzzleInteraction("player1", "sing", "earth")
        assertThat(result).isNull()
    }

    @Test
    fun `should have correct room connections`() {
        assertThat(runicCircle.exits).containsKey(com.terheyden.mud.corelib.Direction.WEST)
        assertThat(runicCircle.exits).containsKey(com.terheyden.mud.corelib.Direction.NORTH)
    }

    @Test
    fun `should have all elemental rune features`() {
        val examinableThings = runicCircle.features.getExaminableThings()
        assertThat(examinableThings).contains("earth rune")
        assertThat(examinableThings).contains("water rune")
        assertThat(examinableThings).contains("fire rune")
        assertThat(examinableThings).contains("air rune")
        assertThat(examinableThings).contains("spirit rune")
    }
}
