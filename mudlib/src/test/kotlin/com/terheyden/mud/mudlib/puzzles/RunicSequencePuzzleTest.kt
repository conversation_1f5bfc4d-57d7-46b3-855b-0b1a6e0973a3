package com.terheyden.mud.mudlib.puzzles

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class RunicSequencePuzzleTest {

    private lateinit var puzzle: RunicSequencePuzzle

    @BeforeEach
    fun setUp() {
        puzzle = RunicSequencePuzzle()
    }

    @Test
    fun `should start with empty sequence`() {
        assertThat(puzzle.getCurrentSequence()).isEmpty()
        assertThat(puzzle.isComplete()).isFalse
        assertThat(puzzle.getRemainingAttempts()).isEqualTo(3)
    }

    @Test
    fun `should accept correct first rune`() {
        val result = puzzle.activateRune("earth")
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        assertThat(puzzle.getCurrentSequence()).containsExactly("earth")
    }

    @Test
    fun `should reject wrong first rune`() {
        val result = puzzle.activateRune("fire")
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.WRONG_SEQUENCE)
        assertThat(puzzle.getCurrentSequence()).isEmpty()
        assertThat(puzzle.getRemainingAttempts()).isEqualTo(2)
    }

    @Test
    fun `should complete sequence with correct order`() {
        assertThat(puzzle.activateRune("earth")).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        assertThat(puzzle.activateRune("water")).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        assertThat(puzzle.activateRune("fire")).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        assertThat(puzzle.activateRune("air")).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        val result = puzzle.activateRune("spirit")
        
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.SEQUENCE_COMPLETE)
        assertThat(puzzle.isComplete()).isTrue
    }

    @Test
    fun `should fail after maximum attempts`() {
        puzzle.activateRune("fire") // Wrong, attempt 1
        puzzle.activateRune("water") // Wrong, attempt 2
        val result = puzzle.activateRune("air") // Wrong, attempt 3
        
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.FAILED)
        assertThat(puzzle.getRemainingAttempts()).isEqualTo(0)
    }

    @Test
    fun `should reject invalid rune names`() {
        val result = puzzle.activateRune("invalid")
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.INVALID_RUNE)
        assertThat(puzzle.getCurrentSequence()).isEmpty()
    }

    @Test
    fun `should reject already activated runes`() {
        puzzle.activateRune("earth")
        val result = puzzle.activateRune("earth")
        
        assertThat(result).isEqualTo(RunicSequencePuzzle.ActivationResult.ALREADY_ACTIVATED)
    }

    @Test
    fun `should provide rune descriptions`() {
        val description = puzzle.getRuneDescription("earth")
        assertThat(description).isNotNull
        assertThat(description).contains("brown")
        assertThat(description).contains("dormant")
    }

    @Test
    fun `should update rune descriptions when activated`() {
        puzzle.activateRune("earth")
        val description = puzzle.getRuneDescription("earth")
        assertThat(description).contains("glowing with activated energy")
    }

    @Test
    fun `should provide hints for next step`() {
        val hint = puzzle.getHint()
        assertThat(hint).isNotNull
        assertThat(hint).contains("foundation")
        
        puzzle.activateRune("earth")
        val nextHint = puzzle.getHint()
        assertThat(nextHint).contains("flows")
    }

    @Test
    fun `should reset puzzle state`() {
        puzzle.activateRune("earth")
        puzzle.activateRune("fire") // Wrong sequence
        
        puzzle.reset()
        
        assertThat(puzzle.getCurrentSequence()).isEmpty()
        assertThat(puzzle.getRemainingAttempts()).isEqualTo(3)
        assertThat(puzzle.isComplete()).isFalse
        
        // All runes should be dormant again
        val description = puzzle.getRuneDescription("earth")
        assertThat(description).contains("dormant")
    }

    @Test
    fun `should handle case insensitive rune names`() {
        val result1 = puzzle.activateRune("EARTH")
        assertThat(result1).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
        
        puzzle.reset()
        
        val result2 = puzzle.activateRune("Earth")
        assertThat(result2).isEqualTo(RunicSequencePuzzle.ActivationResult.CORRECT_STEP)
    }

    @Test
    fun `should return all available runes`() {
        val runes = puzzle.getAllRunes()
        assertThat(runes).hasSize(5)
        assertThat(runes.keys).containsExactlyInAnyOrder("earth", "water", "fire", "air", "spirit")
    }
}
