package com.terheyden.mud.mudlib.puzzles

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class LibraryRiddlePuzzleTest {

    private lateinit var puzzle: LibraryRiddlePuzzle

    @BeforeEach
    fun setUp() {
        puzzle = LibraryRiddlePuzzle()
    }

    @Test
    fun `should start with first riddle available`() {
        val riddle = puzzle.getCurrentRiddle()
        assertThat(riddle).isNotNull
        assertThat(riddle!!.question).contains("cities")
        assertThat(puzzle.getAttemptsRemaining()).isEqualTo(3)
        assertThat(puzzle.isComplete()).isFalse
    }

    @Test
    fun `should accept correct answer for first riddle`() {
        val result = puzzle.checkAnswer("map")
        assertThat(result).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
        
        val nextRiddle = puzzle.getCurrentRiddle()
        assertThat(nextRiddle).isNotNull
        assertThat(nextRiddle!!.question).contains("more you take")
    }

    @Test
    fun `should accept alternative correct answers`() {
        val result1 = puzzle.checkAnswer("a map")
        assertThat(result1).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
        
        puzzle.reset()
        
        val result2 = puzzle.checkAnswer("the map")
        assertThat(result2).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
    }

    @Test
    fun `should handle incorrect answers`() {
        val result = puzzle.checkAnswer("wrong answer")
        assertThat(result).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.INCORRECT)
        assertThat(puzzle.getAttemptsRemaining()).isEqualTo(2)
    }

    @Test
    fun `should fail after three incorrect attempts`() {
        puzzle.checkAnswer("wrong1")
        puzzle.checkAnswer("wrong2")
        val result = puzzle.checkAnswer("wrong3")
        
        assertThat(result).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.FAILED)
        assertThat(puzzle.getAttemptsRemaining()).isEqualTo(0)
    }

    @Test
    fun `should complete puzzle after all riddles answered correctly`() {
        // Answer all riddles correctly
        assertThat(puzzle.checkAnswer("map")).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
        assertThat(puzzle.checkAnswer("footsteps")).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
        val result = puzzle.checkAnswer("echo")
        
        assertThat(result).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.COMPLETE)
        assertThat(puzzle.isComplete()).isTrue
        assertThat(puzzle.getCurrentRiddle()).isNull()
    }

    @Test
    fun `should provide hints for current riddle`() {
        val hint = puzzle.getHint()
        assertThat(hint).isNotNull
        assertThat(hint).contains("shows places")
    }

    @Test
    fun `should reset puzzle state`() {
        puzzle.checkAnswer("map")
        puzzle.checkAnswer("wrong")
        
        puzzle.reset()
        
        assertThat(puzzle.getCurrentRiddle()!!.question).contains("cities")
        assertThat(puzzle.getAttemptsRemaining()).isEqualTo(3)
        assertThat(puzzle.isComplete()).isFalse
    }

    @Test
    fun `should handle case insensitive answers`() {
        val result1 = puzzle.checkAnswer("MAP")
        assertThat(result1).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
        
        puzzle.reset()
        
        val result2 = puzzle.checkAnswer("Map")
        assertThat(result2).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE)
    }

    @Test
    fun `should return already complete when puzzle is finished`() {
        // Complete the puzzle
        puzzle.checkAnswer("map")
        puzzle.checkAnswer("footsteps")
        puzzle.checkAnswer("echo")
        
        val result = puzzle.checkAnswer("anything")
        assertThat(result).isEqualTo(LibraryRiddlePuzzle.PuzzleResult.ALREADY_COMPLETE)
    }
}
