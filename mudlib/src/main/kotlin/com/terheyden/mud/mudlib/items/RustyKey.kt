package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * An old rusty key with mysterious symbols.
 */
class RustyKey : Item(
    id = "rusty_key",
    name = "rusty key",
    description = "An old iron key, heavily rusted but still functional. " +
            "Strange symbols are etched into its head.",
    weight = 1,
    isPickupable = true,
    aliases = listOf("key", "iron key", "old key")
) {

    /**
     * Custom examine description that mentions the symbols.
     */
    override fun getExamineDescription(): String {
        return super.getExamineDescription() + " The symbols etched into the key's head seem to " +
                "match ancient magical runes you've seen carved into stone and wood throughout this realm."
    }
}
