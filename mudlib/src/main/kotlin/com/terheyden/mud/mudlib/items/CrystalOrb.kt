package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A magical crystal orb that serves as the centerpiece of the tower chamber.
 */
class CrystalOrb : Item(
    id = "crystal_orb",
    name = "crystal orb",
    description = "A perfectly spherical crystal that pulses with inner light. " +
            "It feels warm to the touch and seems to contain swirling energies.",
    weight = 10,
    isPickupable = false,
    aliases = listOf("orb", "crystal", "sphere", "crystal sphere")
) {

    private var activationCount = 0

    companion object {
        private const val SECOND_ACTIVATION = 2
        private const val THIRD_ACTIVATION = 3
        private const val MAX_ACTIVATIONS = 4
    }

    /**
     * Attempt to activate the orb's magical properties.
     */
    fun activate(): String {
        activationCount++
        return when (activationCount) {
            1 -> "As you touch the crystal orb, it flares with brilliant light. " +
                    "Visions of distant lands flash before your eyes."

            SECOND_ACTIVATION -> "The orb responds to your touch again, showing you glimpses of " +
                    "hidden passages and secret chambers within the tower."

            THIRD_ACTIVATION -> "The crystal orb pulses warmly. You sense it has shown you all " +
                    "it can for now, but its power remains strong."

            else -> "The orb glows softly but reveals nothing new. Perhaps it needs " +
                    "time to recharge its mystical energies."
        }
    }

    /**
     * Get the current power level of the orb.
     */
    fun getPowerLevel(): String {
        return when (activationCount) {
            0 -> "The orb pulses with untapped potential."
            in 1..SECOND_ACTIVATION -> "The orb's energy feels partially depleted but still strong."
            THIRD_ACTIVATION -> "The orb's power is stable but dormant."
            else -> "The orb needs time to restore its magical energies."
        }
    }

    /**
     * Check if the orb can provide visions.
     */
    fun canProvideVisions(): Boolean = activationCount < MAX_ACTIVATIONS

    /**
     * Custom examine description that reflects the orb's current state.
     */
    override fun getExamineDescription(): String {
        val baseDescription = super.getExamineDescription()
        val powerDescription = getPowerLevel()
        return "$baseDescription $powerDescription"
    }
}
