package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A shard from the mystical mirrors of the maze.
 */
class MirrorShard : Item(
    id = "mirror_shard",
    name = "mirror shard",
    description = "A perfectly smooth shard of mirror that seems to contain infinite depth. " +
            "Unlike ordinary mirrors, this fragment shows not just your reflection, but " +
            "glimpses of truth and hidden knowledge. The edges are surprisingly safe to " +
            "handle, as if the mirror's magic protects those who have earned its trust.",
    aliases = listOf("shard", "mirror", "glass shard", "mystical shard"),
    weight = 1,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You gaze into the mirror shard and see beyond your own reflection. For a moment, " +
                "you glimpse hidden truths about your surroundings - secret passages, hidden " +
                "dangers, and concealed treasures become visible. The knowledge fades quickly, " +
                "but leaves you with a sense of enhanced perception and understanding."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The mirror shard seems to pulse gently with inner light. When you look into it " +
                "from different angles, you catch glimpses of places you've been and paths you " +
                "might take. This could be a powerful tool for revealing hidden secrets."
    }
}
