package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.VillageNotice
import com.terheyden.mud.mudlib.npcs.TravelingBard
import com.terheyden.mud.mudlib.npcs.VillageElder
import org.springframework.stereotype.Component

/**
 * The heart of a small village, bustling with activity and friendly faces.
 */
@Component
class VillageSquare : Room(
    id = "village_square",
    name = "Village Square",
    description = "You stand in the village square. Cobblestones pave the ground around " +
            "a stone fountain. Wooden benches sit nearby, and flower boxes brighten the " +
            "surrounding buildings. The smell of fresh bread drifts from a bakery.",
    features = mutableListOf(
        RoomFeature(
            id = "stone_fountain",
            names = listOf("stone fountain", "fountain", "bubbling fountain"),
            description = "A stone fountain with a carved maiden pouring water from an urn. " +
                    "Water bubbles up and flows down. Coins glint at the bottom.",
            keywords = listOf("carved", "maiden", "urn", "bubbles", "flows", "coins", "glint")
        ),
        RoomFeature(
            id = "wooden_benches",
            names = listOf("wooden benches", "benches", "village benches"),
            description = "Oak benches sit around the fountain, worn smooth by use. " +
                    "Small carvings mark the wood - initials, hearts, and simple drawings.",
            keywords = listOf("oak", "worn", "smooth", "carvings", "initials", "hearts", "drawings")
        ),
        RoomFeature(
            id = "flower_boxes",
            names = listOf("flower boxes", "flowers", "colorful flowers", "window boxes"),
            description = "Flower boxes hang from the windows, filled with red geraniums, " +
                    "purple petunias, and yellow marigolds. Bees buzz among the blooms.",
            keywords = listOf("geraniums", "petunias", "marigolds", "bees", "blooms")
        ),
        RoomFeature(
            id = "village_notice_board",
            names = listOf("notice board", "board", "village board", "bulletin board"),
            description = "A wooden notice board near the fountain displays announcements, " +
                    "job postings, and village news. Iron nails hold the notices in place.",
            keywords = listOf("wooden", "announcements", "postings", "news", "nails")
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to ForestClearing::class,
        Direction.SOUTH to VillageInn::class,
        Direction.EAST to VillageMarket::class,
        Direction.WEST to VillageTemple::class,
    ),
    items = mutableListOf(
        VillageElder(),
        TravelingBard(),
        VillageNotice(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The village square could have special events
        // - Market days with special vendors
        // - Festivals and celebrations
        // - Village meetings and announcements
    }
}
