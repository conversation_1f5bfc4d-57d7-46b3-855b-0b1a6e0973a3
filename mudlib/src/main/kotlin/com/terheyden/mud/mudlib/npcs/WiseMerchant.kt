package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.weapons.IronSword

/**
 * A wise old merchant who offers helpful advice and occasionally gives items to worthy adventurers.
 */
class WiseMerchant : NPC(
    id = "wise_merchant",
    name = "wise merchant",
    description = "An elderly merchant with kind eyes and weathered hands. His robes are simple but well-made, " +
            "and he carries a large pack filled with various goods. He seems to have traveled far and wide.",
    inventory = mutableListOf(HealingPotion(), IronSword()),
    maxInventoryWeight = 100,
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 3,
    baseAttackPower = 5, // Peaceful merchant, low attack
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0, // No reward for killing friendly NPCs
    lootTable = listOf(HealingPotion()), // Drops a healing potion if somehow killed
) {

    private var hasGivenSword = false
    private var conversationCount = 0

    override fun getGreeting(player: Player): String {
        return when (conversationCount) {
            0 -> "Greetings, young adventurer! I am a traveling merchant. " +
                    "These lands can be dangerous - perhaps I can offer some wisdom?"

            else -> "Welcome back, friend! How may I assist you today?"
        }
    }

    override fun handleDialogue(player: Player, message: String): String {
        conversationCount++
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("help") || lowerMessage.contains("advice") -> {
                "The forest can be treacherous, especially to the east where wild beasts roam. " +
                        "Always be prepared for combat, and remember - experience comes from facing challenges!"
            }

            lowerMessage.contains("weapon") || lowerMessage.contains("sword") -> {
                if (!hasGivenSword && player.level >= 2) {
                    hasGivenSword = true
                    val sword = IronSword()
                    player.getCurrentRoom().addItem(sword)
                    "You seem experienced enough to handle a real weapon. Take this iron sword - " +
                            "you'll need it for the dangers ahead!"
                } else if (!hasGivenSword) {
                    "You're still too inexperienced for a real weapon. Gain some experience first, " +
                            "then come back and ask me about weapons."
                } else {
                    "I've already given you my finest sword. Use it well!"
                }
            }

            lowerMessage.contains("trade") || lowerMessage.contains("buy") || lowerMessage.contains("sell") -> {
                "I'm afraid I don't have a proper shop set up here. But I might have something " +
                        "for a worthy adventurer who asks the right questions..."
            }

            lowerMessage.contains("danger") || lowerMessage.contains("beast") || lowerMessage.contains("wolf") -> {
                "Aye, there's a fierce wolf that prowls the eastern caves. Many travelers have " +
                        "fallen to its fangs. If you must face it, make sure you're well-armed and experienced."
            }

            lowerMessage.contains("health") || lowerMessage.contains("healing") || lowerMessage.contains("potion") -> {
                if (player.currentHealthPoints < player.maxHealthPoints * 0.7) {
                    val potion = HealingPotion()
                    player.getCurrentRoom().addItem(potion)
                    "You look wounded, friend. Take this healing potion - you'll need your strength."
                } else {
                    "You look healthy enough to me. Save your coin for when you truly need healing."
                }
            }

            lowerMessage.contains("tower") -> {
                "The ancient tower to the north holds many secrets. I've heard tell of a magical orb " +
                        "at its peak that can show visions of distant places."
            }

            lowerMessage.contains("crystal") || lowerMessage.contains("cave") -> {
                "The crystal cave to the east is beautiful but dangerous. The magical energy there " +
                        "attracts both wondrous crystals and fierce creatures."
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Safe travels, adventurer! May your blade stay sharp and your courage never falter."
            }

            else -> {
                val responses = listOf(
                    "Interesting... tell me more about your adventures.",
                    "I've traveled many lands and seen much. What brings you to these parts?",
                    "The world is full of mysteries, young one. Keep your eyes open and your mind sharp.",
                    "Every adventurer has a story. What's yours?",
                    "These old bones have seen much. Perhaps you'd like to hear about the local dangers?"
                )
                responses.random()
            }
        }
    }
}
