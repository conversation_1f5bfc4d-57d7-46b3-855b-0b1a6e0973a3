package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A notice board posting with village news and information.
 */
class VillageNotice : Item(
    id = "village_notice",
    name = "village notice",
    description = "A piece of parchment pinned to the village notice board with an iron nail. " +
            "The writing is clear and neat, obviously written by someone with education. " +
            "The notice appears to contain important information for travelers and villagers.",
    aliases = listOf("notice", "parchment", "posting", "announcement"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("=== VILLAGE NOTICE ===")
            appendLine()
            appendLine("ATTENTION TRAVELERS AND ADVENTURERS:")
            appendLine()
            appendLine("• The Prancing Pony Inn offers comfortable rooms and hot meals")
            appendLine("• Strange lights have been reported in the abandoned mine - CAUTION ADVISED")
            appendLine("• The village shop has new supplies from the capital")
            appendLine("• Temple services available for healing and blessings")
            appendLine("• Missing persons: Three villagers last seen near the old mine")
            appendLine("• Reward offered for information about the missing villagers")
            appendLine()
            appendLine("For more information, speak with the Village Elder")
            appendLine()
            appendLine("- Village Council")
        }
    }
}
