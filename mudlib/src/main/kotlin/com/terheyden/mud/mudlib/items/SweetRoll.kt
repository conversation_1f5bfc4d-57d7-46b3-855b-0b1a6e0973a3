package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A delicious sweet roll from the village bakery.
 */
class SweetRoll : Item(
    id = "sweet_roll",
    name = "sweet roll",
    description = "A golden-brown sweet roll that's still warm from the oven. The pastry is " +
            "soft and fluffy, with a delicate sweetness enhanced by a hint of cinnamon. " +
            "The top is glazed with a light sugar coating that glistens in the light. " +
            "The aroma is absolutely heavenly and makes your mouth water.",
    aliases = listOf("roll", "sweet", "pastry", "cinnamon roll"),
    weight = 1,
    isPickupable = true
), Useable {

    private var hasBeenEaten = false

    override fun use(user: Player): String {
        return if (!hasBeenEaten) {
            hasBeenEaten = true
            user.heal(8)
            "You bite into the sweet roll and are rewarded with an explosion of flavor. " +
                    "The pastry is incredibly soft and the cinnamon-sugar filling melts " +
                    "on your tongue. The sweetness lifts your spirits and the warmth " +
                    "fills your belly. This is clearly the work of a master baker!"
        } else {
            "You've already eaten the sweet roll. Only crumbs remain."
        }
    }

    override fun getExamineDescription(): String {
        return if (!hasBeenEaten) {
            super.getExamineDescription() + "\n\nThe sweet roll looks absolutely delicious. " +
                    "You could 'eat' or 'use' it to enjoy this tasty treat."
        } else {
            "Only a few sweet crumbs remain of what was once a delicious sweet roll."
        }
    }

    override fun getShortDescription(): String {
        return if (hasBeenEaten) {
            "sweet roll crumbs"
        } else {
            name
        }
    }
}
