package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A hearty meat pie from the village bakery.
 */
class MeatPie : Item(
    id = "meat_pie",
    name = "meat pie",
    description = "A substantial meat pie with a golden, flaky crust that's still warm from " +
            "the oven. Steam escapes from small vents in the top, carrying the rich aroma " +
            "of seasoned beef, vegetables, and savory gravy. The crust looks perfectly " +
            "baked - crispy on the outside but tender within. This is clearly a filling, " +
            "satisfying meal.",
    aliases = listOf("pie", "meat", "beef pie", "savory pie"),
    weight = 3,
    isPickupable = true
), Useable {

    private var portionsRemaining = 4

    override fun use(user: Player): String {
        return if (portionsRemaining > 0) {
            portionsRemaining--
            val healAmount = when (portionsRemaining) {
                3 -> 15 // First portion
                2 -> 12 // Second portion  
                1 -> 10 // Third portion
                0 -> 8  // Last portion
                else -> 0
            }
            user.heal(healAmount)
            
            when (portionsRemaining) {
                3 -> "You cut into the meat pie and take a generous portion. The crust " +
                        "flakes perfectly and the filling is rich with tender beef, carrots, " +
                        "onions, and thick gravy. The flavors are perfectly balanced and " +
                        "incredibly satisfying."
                        
                2 -> "You take another portion of the delicious meat pie. The beef is " +
                        "so tender it falls apart at the touch of your fork, and the " +
                        "vegetables add wonderful texture and flavor to each bite."
                        
                1 -> "You continue eating the meat pie, savoring the rich, hearty flavors. " +
                        "There's still one more portion left, but you're already feeling " +
                        "quite satisfied."
                        
                0 -> "You finish the last of the meat pie, scraping up every bit of " +
                        "gravy and crust. Your belly is full and warm, and you feel " +
                        "completely satisfied. That was an excellent meal!"
                        
                else -> "There's no pie left to eat."
            }
        } else {
            "The pie has been completely eaten. Only an empty plate remains."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = when (portionsRemaining) {
            4 -> "The pie is whole and untouched, ready to provide a hearty meal."
            3 -> "About three-quarters of the pie remains."
            2 -> "About half of the pie is left."
            1 -> "Only a small portion of the pie remains."
            0 -> "The pie has been completely eaten, leaving only an empty plate."
            else -> "The pie is in an unknown state."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription"
    }

    override fun getShortDescription(): String {
        return when (portionsRemaining) {
            4 -> name
            3 -> "partially eaten meat pie"
            2 -> "half-eaten meat pie"
            1 -> "small portion of meat pie"
            0 -> "empty pie plate"
            else -> name
        }
    }
}
