package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import org.springframework.stereotype.Component

/**
 * Peaceful forest path.
 */
@Component
class ForestPath : Room(
    id = "forest_path",
    name = "Forest Path",
    description = "You walk along a dirt path winding through a quiet forest. Birds chirp " +
            "overhead and leaves rustle with wildlife. The village square lies to the south.",
    features = mutableListOf(
    ),
    exits = mutableMapOf(
        Direction.NORTH to ForestClearing::class,
        Direction.SOUTH to VillageSquare::class,
    ),
)
