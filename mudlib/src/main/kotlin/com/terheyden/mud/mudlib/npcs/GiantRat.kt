package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player

/**
 * A large, aggressive rat that inhabits dark tunnels.
 */
class GiantRat : NPC(
    id = "giant_rat",
    name = "giant rat",
    description = "A rat the size of a small dog, with matted brown fur and gleaming red eyes. " +
            "Its yellow teeth are bared in a permanent snarl, and its long, hairless tail " +
            "whips back and forth aggressively. This creature has clearly thrived in the " +
            "dark tunnels, growing far larger than any normal rat should.",
    maxHealthPoints = 25,
    currentHealthPoints = 25,
    level = 1,
    baseAttackPower = 8,
    baseDefense = 2,
    npcType = NPCType.AGGRESSIVE,
    experienceReward = 10,
    lootTable = emptyList(),
) {

    override fun getGreeting(player: Player): String {
        return "The giant rat hisses and bares its yellowed fangs!"
    }

    override fun handleDialogue(player: Player, message: String): String {
        // Rats don't understand speech
        return listOf(
            "The giant rat squeaks angrily in response.",
            "The rat's red eyes glare at you with feral intelligence.",
            "The creature chitches and gnashes its teeth menacingly.",
            "The giant rat's tail lashes back and forth as it prepares to attack."
        ).random()
    }

    override fun onPlayerEnter(player: Player): String {
        return "A giant rat emerges from the shadows, its red eyes gleaming with hunger and malice!"
    }
}
