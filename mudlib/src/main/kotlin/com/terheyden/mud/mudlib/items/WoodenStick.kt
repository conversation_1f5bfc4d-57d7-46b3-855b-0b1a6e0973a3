package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.weapon.Weapon
import com.terheyden.mud.corelib.weapon.WeaponType

/**
 * A sturdy wooden stick that can be used as a walking stick or simple weapon.
 */
class WoodenStick : Weapon(
    id = "wooden_stick",
    name = "wooden stick",
    description = "A sturdy oak branch that has fallen from one of the trees. " +
            "It could be useful as a walking stick or simple weapon.",
    weight = 2,
    aliases = listOf("stick", "branch", "wood", "oak branch"),
    damage = 8,
    weaponType = WeaponType.IMPROVISED,
    durability = 50,
) {

    /**
     * Custom behavior when examining the stick.
     */
    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("It feels solid and well-balanced in your hands, though it's clearly just a makeshift weapon.")
        }
    }
}
