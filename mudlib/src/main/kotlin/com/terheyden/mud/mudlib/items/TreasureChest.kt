package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.CloseLockContainer

/**
 * A mysterious treasure chest that may contain valuable items.
 */
class TreasureChest : CloseLockContainer(
    id = "treasure_chest",
    name = "an ornate treasure chest",
    description = "A beautifully crafted wooden chest bound with brass fittings and intricate " +
            "carvings. The wood is dark mahogany, polished to a rich shine, and the brass " +
            "has been worked into decorative patterns of vines and flowers. A heavy brass " +
            "lock secures the chest, but it appears to be unlocked.",
    aliases = listOf("ornate treasure chest", "chest", "treasure", "wooden chest", "ornate chest"),
    weight = 15,
    isPickupable = false,
    maxNumItems = 5,
    maxWeight = 50,
    items = mutableListOf(
        GoldCoins(),
        SilverRing(),
        HealingPotion(),
    ),
    closed = true,
)
