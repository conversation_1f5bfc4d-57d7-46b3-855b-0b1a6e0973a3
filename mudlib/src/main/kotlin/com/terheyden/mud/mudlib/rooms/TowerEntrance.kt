package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.RustyKey
import org.springframework.stereotype.Component

/**
 * The entrance to the ancient stone tower.
 */
@Component
class TowerEntrance : Room(
    id = "tower_entrance",
    name = "Ancient Tower Entrance",
    description = "Before you stands an imposing stone tower, its weathered walls covered in ivy. " +
            "The heavy wooden door is slightly ajar, revealing darkness within. " +
            "Strange runes are carved into the stone archway above the entrance.",
    features = mutableListOf(
        RoomFeature(
            id = "strange_runes",
            names = listOf("strange runes", "runes", "carvings", "symbols"),
            description = "The runes carved into the stone archway are unlike anything you've seen before. " +
                    "They seem to shimmer slightly in the light, and you feel a faint magical energy " +
                    "emanating from them. The symbols appear to be some form of ancient protective ward.",
            keywords = listOf("archway", "stone", "magic", "magical", "ward", "protection", "ancient")
        ),
        RoomFeature(
            id = "weathered_walls",
            names = listOf("weathered walls", "walls", "stone walls", "tower walls"),
            description = "The tower's walls are made of massive stone blocks, worn smooth by centuries " +
                    "of wind and rain. Ivy creeps up the sides, and you can see small gaps between " +
                    "some stones where mortar has crumbled away.",
            keywords = listOf("stone", "blocks", "mortar", "centuries", "worn")
        ),
        RoomFeature(
            id = "ivy",
            names = listOf("ivy", "vines", "creeping ivy"),
            description = "Thick ivy vines cover much of the tower's lower walls. The leaves are a deep " +
                    "green, and some of the vines are as thick as your arm. They seem to have been " +
                    "growing here for decades, perhaps centuries.",
            keywords = listOf("vines", "leaves", "green", "thick", "growing")
        ),
        RoomFeature(
            id = "wooden_door",
            names = listOf("wooden door", "door", "heavy door"),
            description = "The heavy wooden door is made of dark oak, reinforced with iron bands. " +
                    "It's slightly ajar, revealing only darkness beyond. The wood is weathered but " +
                    "still solid, and the iron hinges show signs of recent use.",
            keywords = listOf("oak", "iron", "bands", "hinges", "dark", "darkness", "ajar")
        ),
    ),
    exits = mutableMapOf(
        Direction.SOUTH to ForestClearing::class,
        Direction.UP to TowerChamber::class,
        Direction.DOWN to TowerBasement::class,
    ),
    items = mutableListOf(RustyKey())
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add atmospheric effects or check for special conditions
        // For example, the runes might glow when certain players approach
    }
}
