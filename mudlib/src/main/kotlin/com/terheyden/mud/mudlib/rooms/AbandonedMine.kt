package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldNugget
import com.terheyden.mud.mudlib.npcs.CaveTroll
import com.terheyden.mud.mudlib.weapons.MiningPickaxe
import org.springframework.stereotype.Component

/**
 * An old mining operation, now home to dangerous creatures and forgotten treasures.
 */
@Component
class AbandonedMine : Room(
    id = "abandoned_mine",
    name = "Abandoned Mine",
    description = "You enter an old mine shaft with wooden support beams creaking in " +
            "the stale air. Rusted mining equipment lies scattered about, and the walls " +
            "show signs of excavation. Cart tracks lead into the darkness. You can hear " +
            "dripping water. The air smells of earth, rust, and danger.",
    features = mutableListOf(
        RoomFeature(
            id = "wooden_supports",
            names = listOf("wooden supports", "support beams", "beams", "wooden beams"),
            description = "Thick wooden beams support the mine entrance, their surfaces darkened " +
                    "with age and moisture. Some beams show signs of stress - small cracks " +
                    "and warping that suggest the mine's structural integrity may be " +
                    "compromised. They creak softly with every movement.",
            keywords = listOf(
                "thick",
                "darkened",
                "moisture",
                "stress",
                "cracks",
                "warping",
                "structural",
                "integrity",
                "compromised",
                "creak"
            )
        ),
        RoomFeature(
            id = "mining_equipment",
            names = listOf("mining equipment", "equipment", "rusted equipment", "tools"),
            description = "Old mining tools are scattered throughout the entrance - pickaxes with " +
                    "broken handles, rusted shovels, dented buckets, and coils of frayed rope. " +
                    "Everything is covered in a thick layer of dust and shows signs of hasty " +
                    "abandonment, as if the miners left in a hurry.",
            keywords = listOf(
                "pickaxes",
                "broken",
                "handles",
                "shovels",
                "dented",
                "buckets",
                "coils",
                "frayed",
                "rope",
                "dust",
                "hasty",
                "abandonment",
                "hurry"
            )
        ),
        RoomFeature(
            id = "cart_tracks",
            names = listOf("cart tracks", "tracks", "mine tracks", "rails"),
            description = "Iron rails run along the mine floor, designed to guide mining carts " +
                    "loaded with ore. The rails are rusted and some sections are bent or " +
                    "missing entirely. A few overturned carts lie beside the tracks, their " +
                    "contents long since spilled and scattered.",
            keywords = listOf(
                "iron",
                "rails",
                "guide",
                "carts",
                "ore",
                "rusted",
                "bent",
                "missing",
                "overturned",
                "contents",
                "spilled",
                "scattered"
            )
        ),
        RoomFeature(
            id = "excavated_walls",
            names = listOf("excavated walls", "walls", "mine walls", "rock walls"),
            description = "The walls show clear evidence of systematic excavation, with tool marks " +
                    "and blast patterns visible in the rock. Veins of different minerals " +
                    "run through the stone, some glinting with metallic traces. Dark stains " +
                    "on the walls could be from water... or something else.",
            keywords = listOf(
                "systematic",
                "tool",
                "marks",
                "blast",
                "patterns",
                "veins",
                "minerals",
                "glinting",
                "metallic",
                "traces",
                "stains"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to DungeonChamber::class,
    ),
    items = mutableListOf(
        CaveTroll(),
        MiningPickaxe(),
        GoldNugget(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The mine could have dangerous effects
        // - Cave-ins and structural collapses
        // - Toxic gases
        // - Dangerous creatures
        // - Hidden treasure veins
    }
}
