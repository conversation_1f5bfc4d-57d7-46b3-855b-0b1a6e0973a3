package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.Living
import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.weapons.IronSword

/**
 * An undead skeleton warrior that guards ancient treasures.
 */
class SkeletonWarrior : NPC(
    id = "skeleton_warrior",
    name = "skeleton warrior",
    description = "The animated remains of an ancient warrior, held together by dark magic. " +
            "Yellowed bones gleam in the dim light, and empty eye sockets burn with an " +
            "unholy fire. Tattered remnants of armor still cling to the skeletal frame, " +
            "and it moves with the fluid grace of a seasoned fighter despite being undead.",
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 4,
    baseAttackPower = 16,
    baseDefense = 8,
    npcType = NPCType.AGGRESSIVE,
    experienceReward = 40,
    lootTable = listOf(GoldCoins(), IronSword()),
) {

    override fun getGreeting(player: Player): String {
        return "The skeleton warrior's jaw opens in a silent scream as it raises its weapon!"
    }

    override fun handleDialogue(player: Player, message: String): String {
        // Skeletons don't understand speech, but they might react to certain words
        val lowerMessage = message.lowercase()
        
        return when {
            lowerMessage.contains("peace") || lowerMessage.contains("rest") -> {
                "The skeleton pauses momentarily, as if some distant memory stirs, but then " +
                        "the unholy fire in its eyes flares brighter and it prepares to attack."
            }
            lowerMessage.contains("death") || lowerMessage.contains("die") -> {
                "The skeleton's bones rattle with what might be laughter, a dry, hollow sound " +
                        "that echoes through the chamber."
            }
            lowerMessage.contains("magic") || lowerMessage.contains("spell") -> {
                "The dark magic animating the skeleton pulses visibly through its bones, " +
                        "responding to your words about magic."
            }
            else -> {
                val responses = listOf(
                    "The skeleton warrior tilts its skull, empty sockets fixed on you menacingly.",
                    "Bones rattle as the skeleton shifts its stance, preparing for combat.",
                    "The unholy fire in the skeleton's eyes flickers, but it shows no understanding.",
                    "The skeleton's jaw moves soundlessly, as if trying to speak but producing no words.",
                    "Ancient armor clinks as the skeleton warrior readies its weapon."
                )
                responses.random()
            }
        }
    }

    override fun onPlayerEnter(player: Player): String {
        return "As you enter the chamber, ancient bones begin to stir and reassemble themselves! " +
                "A skeleton warrior rises from the floor, its empty eye sockets blazing with " +
                "unholy fire. It draws a rusted sword and advances toward you with deadly intent!"
    }

    override fun getCombatAction(target: Living): String {
        // Skeletons fight with relentless aggression
        return "attack"
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This undead warrior was once a formidable fighter, and death has not " +
                    "diminished its combat skills. Dark magic holds its bones together and " +
                    "drives it to attack any living creature that enters its domain.")
            appendLine("The tattered armor suggests this warrior died long ago, perhaps in " +
                    "the service of the ancient wizard who built the tower above.")
        }
    }

    /**
     * Skeletons are resistant to some damage but vulnerable to others.
     */
    override fun takeDamage(damage: Int): Boolean {
        // Skeletons take reduced damage from slashing weapons but normal from blunt
        val actualDamage = if (damage > 10) {
            (damage * 0.8).toInt() // 20% damage reduction for high damage attacks
        } else {
            damage
        }
        
        return super.takeDamage(actualDamage)
    }
}
