package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A intricate clockwork key that can wind mechanical devices.
 */
class ClockworkKey : Item(
    id = "clockwork_key",
    name = "clockwork key",
    description = "An intricate key made of brass and steel, with tiny gears and springs " +
            "visible through crystal windows in its shaft. The key seems to pulse with " +
            "mechanical energy, and you can hear the faint ticking of internal mechanisms. " +
            "It appears designed to wind or activate complex clockwork devices.",
    aliases = listOf("key", "brass key", "mechanical key", "winding key"),
    weight = 1,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You turn the clockwork key in your hands, listening to the satisfying clicks " +
                "and whirs of its internal mechanisms. The key seems to store mechanical energy " +
                "as you wind it, ready to transfer that power to other clockwork devices. " +
                "The tiny gears spin faster, and the crystal windows glow with stored energy."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "Through the crystal windows, you can see miniature gears, springs, and " +
                "escapements working in perfect harmony. This is clearly the work of a " +
                "master clockmaker, designed to interface with the most complex mechanical " +
                "devices."
    }
}
