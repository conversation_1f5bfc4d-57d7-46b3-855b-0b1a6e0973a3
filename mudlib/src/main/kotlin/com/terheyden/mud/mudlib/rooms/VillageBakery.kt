package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.MeatPie
import com.terheyden.mud.mudlib.items.SweetRoll
import com.terheyden.mud.mudlib.npcs.BakersApprentice
import com.terheyden.mud.mudlib.npcs.VillageBaker
import org.springframework.stereotype.Component

/**
 * A warm bakery filled with the delicious aroma of fresh bread and pastries.
 */
@Component
class VillageBakery : Room(
    id = "village_bakery",
    name = "Village Bakery",
    description = "You enter a warm bakery filled with the aroma of fresh bread and pastries. " +
            "A large stone oven dominates one wall, its fire crackling. Wooden shelves display " +
            "golden loaves, pastries, and meat pies. Flour dusts the surfaces.",
    features = mutableListOf(
        RoomFeature(
            id = "stone_oven",
            names = listOf("stone oven", "oven", "baking oven", "large oven"),
            description = "A massive stone oven built into the wall provides the heart of the " +
                    "bakery's operation. The oven is heated by a wood fire that burns steadily, " +
                    "maintaining the perfect temperature for baking. The stone retains heat " +
                    "evenly, ensuring that bread bakes to golden perfection. The oven door " +
                    "is made of heavy iron with a long wooden handle.",
            keywords = listOf(
                "massive",
                "stone",
                "built",
                "wall",
                "heart",
                "operation",
                "heated",
                "wood",
                "fire",
                "burns",
                "steadily",
                "maintaining",
                "perfect",
                "temperature",
                "baking",
                "retains",
                "heat",
                "evenly",
                "ensuring",
                "bread",
                "bakes",
                "golden",
                "perfection",
                "door",
                "heavy",
                "iron",
                "long",
                "wooden",
                "handle"
            )
        ),
        RoomFeature(
            id = "display_cases",
            names = listOf("display cases", "cases", "bakery displays", "food displays"),
            description = "Glass-fronted wooden cases display the bakery's fresh goods. Loaves " +
                    "of bread in various shapes and sizes sit alongside sweet rolls, fruit " +
                    "tarts, and savory pies. Everything looks freshly made and appetizing, " +
                    "with steam still rising from some of the warmer items. Small signs " +
                    "indicate prices and ingredients.",
            keywords = listOf(
                "glass-fronted",
                "wooden",
                "cases",
                "display",
                "fresh",
                "goods",
                "loaves",
                "bread",
                "various",
                "shapes",
                "sizes",
                "alongside",
                "sweet",
                "rolls",
                "fruit",
                "tarts",
                "savory",
                "pies",
                "freshly",
                "made",
                "appetizing",
                "steam",
                "rising",
                "warmer",
                "items",
                "small",
                "signs",
                "prices",
                "ingredients"
            )
        ),
        RoomFeature(
            id = "work_tables",
            names = listOf("work tables", "tables", "baking tables", "wooden tables"),
            description = "Large wooden work tables are covered with flour and show the signs " +
                    "of constant use. Rolling pins, mixing bowls, measuring cups, and other " +
                    "baking tools are scattered across the surfaces. Balls of dough in various " +
                    "stages of preparation wait their turn in the oven.",
            keywords = listOf(
                "large",
                "wooden",
                "work",
                "tables",
                "covered",
                "flour",
                "signs",
                "constant",
                "use",
                "rolling",
                "pins",
                "mixing",
                "bowls",
                "measuring",
                "cups",
                "baking",
                "tools",
                "scattered",
                "surfaces",
                "balls",
                "dough",
                "various",
                "stages",
                "preparation",
                "wait",
                "turn",
                "oven"
            )
        ),
        RoomFeature(
            id = "flour_sacks",
            names = listOf("flour sacks", "sacks", "grain sacks", "flour bags"),
            description = "Large burlap sacks of flour and grain are stacked against one wall. " +
                    "The sacks are labeled with different types - wheat flour, rye flour, " +
                    "oat flour, and others. Some sacks are open, with wooden scoops for " +
                    "measuring out the needed amounts. A fine dusting of flour covers " +
                    "everything nearby.",
            keywords = listOf(
                "large",
                "burlap",
                "sacks",
                "flour",
                "grain",
                "stacked",
                "wall",
                "labeled",
                "different",
                "types",
                "wheat",
                "rye",
                "oat",
                "open",
                "wooden",
                "scoops",
                "measuring",
                "needed",
                "amounts",
                "fine",
                "dusting",
                "covers",
                "everything",
                "nearby"
            )
        ),
        RoomFeature(
            id = "cooling_racks",
            names = listOf("cooling racks", "racks", "bread racks", "wire racks"),
            description = "Wire cooling racks hold freshly baked goods that are too hot to " +
                    "display yet. Steam rises from warm loaves and pastries as they cool " +
                    "to the perfect temperature. The racks are arranged to allow air to " +
                    "circulate freely around the baked goods.",
            keywords = listOf(
                "wire",
                "cooling",
                "racks",
                "hold",
                "freshly",
                "baked",
                "goods",
                "hot",
                "display",
                "steam",
                "rises",
                "warm",
                "loaves",
                "pastries",
                "cool",
                "perfect",
                "temperature",
                "arranged",
                "allow",
                "air",
                "circulate",
                "freely",
                "around"
            )
        ),
        RoomFeature(
            id = "spice_shelf",
            names = listOf("spice shelf", "spices", "baking spices", "seasonings"),
            description = "A wooden shelf holds an array of spices and seasonings used in baking. " +
                    "Small jars contain cinnamon, nutmeg, vanilla, salt, sugar, and other " +
                    "essential ingredients. Each jar is carefully labeled, and the combined " +
                    "aromas create a complex, appetizing scent that mingles with the smell " +
                    "of baking bread.",
            keywords = listOf(
                "wooden",
                "shelf",
                "holds",
                "array",
                "spices",
                "seasonings",
                "used",
                "baking",
                "small",
                "jars",
                "contain",
                "cinnamon",
                "nutmeg",
                "vanilla",
                "salt",
                "sugar",
                "essential",
                "ingredients",
                "carefully",
                "labeled",
                "combined",
                "aromas",
                "create",
                "complex",
                "appetizing",
                "scent",
                "mingles",
                "smell",
                "bread"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to VillageMarket::class,
    ),
    items = mutableListOf(
        VillageBaker(),
        BakersApprentice(),
        BreadLoaf(),
        SweetRoll(),
        MeatPie(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add baking mini-games, special orders, or cooking lessons
    }
}
