package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A set of precision alchemical tools for advanced potion making.
 */
class AlchemicalApparatus : Item(
    id = "alchemical_apparatus",
    name = "alchemical apparatus",
    description = "A beautifully crafted set of alchemical tools made from silver and crystal. " +
            "The set includes precision scales, graduated measuring cylinders, a mortar and " +
            "pestle carved from a single piece of moonstone, and several delicate glass " +
            "instruments whose purpose is not immediately clear. Each tool is perfectly " +
            "balanced and seems to hum with subtle magical energy.",
    aliases = listOf("apparatus", "tools", "alchemical tools", "silver tools", "alchemy set"),
    weight = 5,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the alchemical apparatus closely, marveling at the precision and " +
                "craftsmanship. As you handle the tools, you feel your understanding of " +
                "alchemical principles deepen. The moonstone mortar and pestle feel warm " +
                "to the touch, and the crystal instruments seem to resonate with magical " +
                "energy. With tools like these, you could create potions of incredible potency."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "Each tool in the set bears tiny runic inscriptions that seem to enhance " +
                "their effectiveness. The silver gleams with an inner light, and the crystal " +
                "components are flawlessly clear. This apparatus would be invaluable to any " +
                "serious practitioner of the alchemical arts."
    }
}
