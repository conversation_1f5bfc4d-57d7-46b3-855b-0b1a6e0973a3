package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.Living
import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.HealingPotion

/**
 * A fierce wolf that attacks players on sight. A dangerous predator of the crystal caves.
 */
class FierceWolf : NPC(
    id = "fierce_wolf",
    name = "fierce wolf",
    description = "A large, muscular wolf with piercing yellow eyes and bared fangs. " +
            "Its dark gray fur is matted and scarred from many battles. " +
            "It moves with predatory grace, clearly a dangerous opponent.",
    maxHealthPoints = 60,
    currentHealthPoints = 60,
    level = 2,
    baseAttackPower = 18, // High attack power for a dangerous predator
    baseDefense = 3,
    npcType = NPCType.AGGRESSIVE,
    experienceReward = 25,
    lootTable = listOf(HealingPotion()), // Wolves sometimes have consumed healing herbs
) {

    private var hasAttackedPlayer = false

    override fun getGreeting(player: Player): String {
        // Wolves don't greet - they attack!
        return "The wolf snarls and prepares to attack!"
    }

    override fun handleDialogue(player: Player, message: String): String {
        // Wolves don't understand human speech
        return when {
            message.lowercase().contains("good") || message.lowercase().contains("nice") -> {
                "The wolf tilts its head slightly but continues to growl menacingly."
            }
            message.lowercase().contains("food") || message.lowercase().contains("meat") -> {
                "The wolf's eyes gleam hungrily. It seems to think YOU are the food!"
            }
            else -> {
                val responses = listOf(
                    "The wolf snarls in response, showing its sharp teeth.",
                    "The wolf growls low and threatening, not understanding your words.",
                    "The wolf's ears flatten against its head as it prepares to strike.",
                    "The wolf circles you warily, looking for an opening to attack."
                )
                responses.random()
            }
        }
    }

    override fun onPlayerEnter(player: Player): String {
        hasAttackedPlayer = true
        return "A fierce wolf emerges from the shadows, its yellow eyes fixed on you! " +
                "It bares its fangs and growls menacingly - this beast means to make you its next meal!"
    }

    override fun getCombatAction(target: Living): String {
        // Wolves always attack aggressively
        return "attack"
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This is clearly an apex predator - its powerful muscles ripple beneath its fur, " +
                    "and its claws are sharp and deadly.")
            appendLine("The wolf's stance suggests it's ready to pounce at any moment.")
            
            if (currentHealthPoints < maxHealthPoints * 0.5) {
                appendLine("The wolf is wounded but still dangerous - perhaps even more so now that it's desperate.")
            }
        }
    }

    /**
     * Wolves become more aggressive when wounded.
     */
    override fun takeDamage(damage: Int): Boolean {
        val survived = super.takeDamage(damage)

        // Increase attack power when wounded (berserker rage)
        if (currentHealthPoints < maxHealthPoints * 0.3 && survived) {
            baseAttackPower += 2
        }

        return survived
    }

    /**
     * Wolves have aggressive autonomous behavior.
     */
    override fun performAggressiveBehavior(tickCount: Long) {
        // Wolves are more active - check for threats every 10 ticks
        if (tickCount % 10 == 0L) {
            // TODO: When multi-player support is added, wolves could:
            // - Prowl around their territory
            // - Hunt for prey
            // - Call for pack members when in combat
        }
    }

    /**
     * Wolves tick more frequently due to their aggressive nature.
     */
    override fun getTickInterval(): Int = 5
}
