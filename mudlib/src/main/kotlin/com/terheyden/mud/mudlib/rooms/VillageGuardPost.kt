package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.IronSword
import com.terheyden.mud.mudlib.npcs.VillageGuard
import org.springframework.stereotype.Component

/**
 * The village guard post where the local guards maintain order and security.
 */
@Component
class VillageGuardPost : Room(
    id = "village_guard_post",
    name = "Village Guard Post",
    description = "A sturdy stone building that serves as headquarters for the village guard. " +
            "Weapon racks line the walls, and a large wooden table serves as a command center " +
            "with maps and patrol schedules. The guards here maintain order and protect the " +
            "village from threats.",
    features = mutableListOf(
        RoomFeature(
            id = "weapon_racks",
            names = listOf("weapon racks", "racks", "weapons"),
            description = "Wooden racks hold various weapons used by the village guard.",
            keywords = listOf("wooden", "racks", "various", "weapons", "guard")
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to VillageOutskirts::class,
    ),
    items = mutableListOf(
        VillageGuard(),
        IronSword(),
        GoldCoins(),
    ),
)
