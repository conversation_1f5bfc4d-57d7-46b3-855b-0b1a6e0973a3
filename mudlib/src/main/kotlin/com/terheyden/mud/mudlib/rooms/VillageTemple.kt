package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.BlessedCandle
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.PrayerBook
import com.terheyden.mud.mudlib.npcs.TempleAcolyte
import com.terheyden.mud.mudlib.npcs.TemplePriest
import org.springframework.stereotype.Component

/**
 * A peaceful temple dedicated to healing and spiritual guidance.
 */
@Component
class VillageTemple : Room(
    id = "village_temple",
    name = "Village Temple",
    description = "You enter a peaceful temple with golden light from stained glass windows. " +
            "The air smells of incense. Wooden pews face an altar with candles and flowers. " +
            "Tapestries on the walls show scenes of healing and compassion.",
    features = mutableListOf(
        RoomFeature(
            id = "stained_glass_windows",
            names = listOf("stained glass windows", "windows", "colored windows", "glass windows"),
            description = "Beautiful stained glass windows line the walls, depicting scenes of " +
                    "divine healing, protection, and guidance. The colored glass casts rainbow " +
                    "patterns of light across the temple floor, creating an atmosphere of " +
                    "wonder and reverence. Each window tells a story of faith and hope.",
            keywords = listOf(
                "beautiful",
                "line",
                "walls",
                "depicting",
                "divine",
                "healing",
                "protection",
                "guidance",
                "colored",
                "rainbow",
                "patterns",
                "floor",
                "atmosphere",
                "wonder",
                "reverence",
                "story",
                "faith",
                "hope"
            )
        ),
        RoomFeature(
            id = "temple_altar",
            names = listOf("temple altar", "altar", "sacred altar", "holy altar"),
            description = "A simple but elegant altar made of white marble stands at the front of " +
                    "the temple. It's adorned with burning candles, fresh flowers, and sacred " +
                    "symbols. The altar radiates a gentle warmth and seems to pulse with " +
                    "healing energy. Many offerings and prayers have been left here.",
            keywords = listOf(
                "simple",
                "elegant",
                "white",
                "marble",
                "front",
                "adorned",
                "burning",
                "candles",
                "fresh",
                "flowers",
                "sacred",
                "symbols",
                "radiates",
                "gentle",
                "warmth",
                "pulse",
                "healing",
                "energy",
                "offerings",
                "prayers"
            )
        ),
        RoomFeature(
            id = "wooden_pews",
            names = listOf("wooden pews", "pews", "temple pews", "benches"),
            description = "Rows of polished wooden pews provide seating for worshippers and those " +
                    "seeking solace. Each pew is carved with simple but beautiful designs, and " +
                    "soft cushions make them comfortable for long periods of meditation or prayer. " +
                    "The wood has been worn smooth by countless hands and knees.",
            keywords = listOf(
                "rows",
                "polished",
                "seating",
                "worshippers",
                "seeking",
                "solace",
                "carved",
                "simple",
                "beautiful",
                "designs",
                "soft",
                "cushions",
                "comfortable",
                "meditation",
                "prayer",
                "worn",
                "smooth",
                "countless",
                "hands",
                "knees"
            )
        ),
        RoomFeature(
            id = "healing_shrine",
            names = listOf("healing shrine", "shrine", "sacred shrine", "healing altar"),
            description = "A smaller shrine dedicated specifically to healing stands to one side " +
                    "of the temple. It's surrounded by votive candles and small offerings from " +
                    "those who have been healed. The shrine emanates a powerful aura of " +
                    "restoration and renewal.",
            keywords = listOf(
                "smaller",
                "dedicated",
                "specifically",
                "side",
                "surrounded",
                "votive",
                "candles",
                "small",
                "offerings",
                "healed",
                "emanates",
                "powerful",
                "aura",
                "restoration",
                "renewal"
            )
        ),
        RoomFeature(
            id = "incense_burners",
            names = listOf("incense burners", "incense", "burners", "sacred incense"),
            description = "Several ornate incense burners fill the temple with fragrant smoke. " +
                    "The sweet, calming scent helps create an atmosphere of peace and " +
                    "contemplation. The smoke rises in gentle spirals toward the vaulted ceiling.",
            keywords = listOf(
                "ornate",
                "fragrant",
                "smoke",
                "sweet",
                "calming",
                "scent",
                "create",
                "atmosphere",
                "peace",
                "contemplation",
                "rises",
                "gentle",
                "spirals",
                "vaulted",
                "ceiling"
            )
        ),
        RoomFeature(
            id = "prayer_books",
            names = listOf("prayer books", "books", "holy books", "sacred texts"),
            description = "A collection of prayer books and sacred texts rest on a wooden lectern " +
                    "near the altar. The books are bound in leather and contain prayers, hymns, " +
                    "and wisdom for those seeking spiritual guidance. The pages are well-worn " +
                    "from frequent use.",
            keywords = listOf(
                "collection",
                "sacred",
                "texts",
                "rest",
                "wooden",
                "lectern",
                "near",
                "bound",
                "leather",
                "contain",
                "prayers",
                "hymns",
                "wisdom",
                "seeking",
                "spiritual",
                "guidance",
                "pages",
                "well-worn",
                "frequent",
                "use"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to VillageSquare::class,
        Direction.SOUTH to VillageLibrary::class,
    ),
    items = mutableListOf(
        TemplePriest(),
        TempleAcolyte(),
        HealingPotion(),
        BlessedCandle(),
        PrayerBook(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add blessing effects, healing aura, or spiritual guidance
    }
}
