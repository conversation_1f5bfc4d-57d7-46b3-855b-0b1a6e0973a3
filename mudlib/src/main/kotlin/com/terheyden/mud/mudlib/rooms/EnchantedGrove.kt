package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.EnchantedFlower
import com.terheyden.mud.mudlib.items.MagicalBerries
import com.terheyden.mud.mudlib.npcs.ForestSpirit
import org.springframework.stereotype.Component

/**
 * A mystical grove where nature magic runs strong and ancient spirits dwell.
 */
@Component
class EnchantedGrove : Room(
    id = "enchanted_grove",
    name = "Enchanted Grove",
    description = "You step into a magical grove where the air shimmers with enchantment. " +
            "Ancient trees with silver bark reach skyward, their leaves glowing softly. " +
            "Colorful flowers bloom year-round, and the grass pulses with magical energy. " +
            "Translucent butterflies flutter between trees, leaving sparkling trails.",
    features = mutableListOf(
        RoomFeature(
            id = "silver_trees",
            names = listOf("silver trees", "trees", "ancient trees", "silver bark"),
            description = "Ancient trees with bark that gleams like silver. Their leaves emit " +
                    "a gentle light that changes color with the seasons.",
            keywords = listOf("ancient", "gleams", "silver", "emit", "light", "seasons")
        ),
        RoomFeature(
            id = "magical_flowers",
            names = listOf("magical flowers", "flowers", "impossible flowers", "glowing flowers"),
            description = "Flowers of impossible colors bloom throughout the grove. Some petals " +
                    "shift through rainbow hues, others glow like stars, and a few sing softly " +
                    "in the wind. Their fragrance is magical.",
            keywords = listOf("impossible", "colors", "shift", "rainbow", "glow", "stars", "sing", "magical")
        ),
        RoomFeature(
            id = "living_grass",
            names = listOf("living grass", "grass", "magical grass", "glowing grass"),
            description = "The grass pulses with life and magic. Each blade responds to your " +
                    "presence, bending toward you. It glows faintly with green light.",
            keywords = listOf("pulses", "life", "magic", "blade", "responds", "glows", "green")
        ),
        RoomFeature(
            id = "magical_butterflies",
            names = listOf("butterflies", "magical butterflies", "translucent butterflies", "sparkling butterflies"),
            description = "Ethereal butterflies with translucent wings flutter throughout the grove. " +
                    "Their wings seem made of light, leaving sparkling trails wherever they fly.",
            keywords = listOf("ethereal", "translucent", "wings", "light", "sparkling", "trails")
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to SecretGarden::class,
    ),
    items = mutableListOf(
        ForestSpirit(),
        MagicalBerries(),
        EnchantedFlower(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The grove could have powerful magical effects
        // - Healing and restoration
        // - Magical ability enhancement
        // - Communication with nature spirits
        // - Temporary magical transformations
    }
}
