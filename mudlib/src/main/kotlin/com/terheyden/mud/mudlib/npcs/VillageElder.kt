package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import io.github.oshai.kotlinlogging.KotlinLogging

/**
 * The wise village elder who knows the history and secrets of the land.
 */
class VillageElder : NPC(
    id = "village_elder",
    name = "village elder",
    description = "An elderly man with a long white beard and kind, twinkling eyes. He wears " +
            "simple robes of brown wool and carries a gnarled walking staff. His face is " +
            "lined with age and wisdom, and he moves with the careful dignity of someone " +
            "who has seen many seasons come and go.",
    inventory = mutableListOf(GoldCoins()),
    maxHealthPoints = 60,
    currentHealthPoints = 60,
    level = 5,
    baseAttackPower = 3, // Very peaceful
    baseDefense = 10,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private val logger = KotlinLogging.logger {}
    private var hasSharedHistory = false
    private var hasGivenQuest = false

    override fun getGreeting(player: Player): String {
        return "Ah, a _young adventurer_! Welcome to our *humble village*. I am the elder here, " +
                "keeper of our `stories and traditions`. Please, sit and rest awhile."
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("history") || lowerMessage.contains("story") || lowerMessage.contains("past") -> {
                if (!hasSharedHistory) {
                    hasSharedHistory = true
                    "Ah, you wish to know our history? This village was founded three centuries ago " +
                            "by refugees fleeing a great war. The ancient tower you see was built by " +
                            "a powerful wizard who protected our ancestors. Legend says his spirit " +
                            "still watches over us from the tower's peak."
                } else {
                    "The tower holds many secrets, young one. Some say there are passages beneath " +
                            "it that lead to forgotten chambers filled with the wizard's treasures."
                }
            }

            lowerMessage.contains("tower") || lowerMessage.contains("wizard") -> {
                "The tower has stood for over 300 years, built by the great wizard Aldric the Wise. " +
                        "He was a protector of the innocent and a master of the mystical arts. " +
                        "Some say his magical experiments continue even now, deep beneath the tower."
            }

            lowerMessage.contains("danger") || lowerMessage.contains("monster") || lowerMessage.contains("threat") -> {
                "These lands have grown more dangerous of late. Strange creatures emerge from " +
                        "the old mine, and travelers speak of undead stirring in the ancient " +
                        "burial grounds. The forest spirits seem restless as well."
            }

            lowerMessage.contains("quest") || lowerMessage.contains("task") || lowerMessage.contains("help") -> {
                if (!hasGivenQuest && player.level >= 3) {
                    hasGivenQuest = true
                    "Actually, there is something you could help with. Strange lights have been " +
                            "seen in the abandoned mine, and several villagers have gone missing. " +
                            "If you're brave enough to investigate, the village would be grateful. " +
                            "But be careful - take proper equipment and don't go alone if you can help it."
                } else if (!hasGivenQuest) {
                    "You seem young and inexperienced yet. Gain some experience first, then " +
                            "return to me if you wish to help the village."
                } else {
                    "Have you investigated the strange lights in the mine yet? The missing " +
                            "villagers' families grow more worried each day."
                }
            }

            lowerMessage.contains("mine") || lowerMessage.contains("missing") -> {
                "The old mine was abandoned years ago when the ore ran out, but recently " +
                        "people have seen strange lights coming from within. Three villagers " +
                        "went to investigate and never returned. We fear something evil has " +
                        "made its home there."
            }

            lowerMessage.contains("village") || lowerMessage.contains("people") -> {
                "Our village is small but proud. We have a fine inn, a temple to the old gods, " +
                        "and a shop that serves travelers well. Most importantly, we have each " +
                        "other. In these dangerous times, community is our greatest strength."
            }

            lowerMessage.contains("advice") || lowerMessage.contains("wisdom") -> {
                val advice = listOf(
                    "Remember, young one: courage without wisdom is mere recklessness.",
                    "The greatest treasures are often found in the most dangerous places.",
                    "Trust in your friends, but always be prepared to stand alone.",
                    "Magic is a tool, like any other. It's the heart that wields it that matters.",
                    "Sometimes the greatest victory is knowing when not to fight."
                )
                advice.random()
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "May the old gods watch over you, young adventurer. Return safely to us."
            }

            else -> {
                val responses = listOf(
                    "Tell me, what brings you to our village?",
                    "I sense great potential in you, young one.",
                    "These are troubled times, but heroes like you give us hope.",
                    "Have you heard the stories of the ancient wizard's tower?",
                    "The old ways are not forgotten here, if you know how to look."
                )
                responses.random()
            }
        }
    }

    /**
     * Village elders have wise, contemplative behavior.
     */
    override fun performFriendlyBehavior(tickCount: Long) {
        // Every 15 ticks (about 30 seconds), the elder might say something wise
        if (tickCount % 15 == 0L) {
            logger.debug { "Village Elder checking if should speak on tick $tickCount" }
            // Only speak if there's a chance (30% probability)
            if (kotlin.random.Random.nextFloat() < 0.3f) {
                val wiseSayings = listOf(
                    "The old paths through the forest are not as safe as they once were...",
                    "I remember when this village was just a handful of huts. How it has grown!",
                    "The tower's light seems dimmer these days. I wonder what that portends.",
                    "Young adventurers these days... always in such a hurry. Wisdom comes with patience.",
                    "The seasons change, but the old stories remain. They hold truths worth remembering.",
                    "Strange dreams have been troubling the villagers lately. The spirits are restless.",
                    "In my youth, we knew the names of every tree in the forest. Now, few remember.",
                    "The ancient ways are not lost, merely forgotten. Perhaps it's time to remember them.",
                    "I sense a change coming on the wind. Great deeds will be done before long.",
                    "The mine has been quiet too long. Silence in dark places is rarely a good sign."
                )

                val randomSaying = wiseSayings.random()
                logger.debug { "Village Elder speaking: $randomSaying" }
                sayToRoom(randomSaying)
            }
        }

        // Every 25 ticks (about 50 seconds), the elder might perform an action
        if (tickCount % 25 == 0L) {
            logger.debug { "Village Elder checking if should perform action on tick $tickCount" }
            // Only act if there's a chance (20% probability)
            if (kotlin.random.Random.nextFloat() < 0.2f) {
                val actions = listOf(
                    "strokes his long white beard thoughtfully.",
                    "adjusts his simple brown robes and sighs softly.",
                    "taps his gnarled walking staff against the cobblestones.",
                    "gazes up at the ancient tower with a distant expression.",
                    "nods slowly, as if remembering something from long ago.",
                    "closes his eyes and listens to the sounds of the village.",
                    "examines the fountain with the careful attention of someone who has seen many changes.",
                    "mutters quietly to himself in an ancient dialect."
                )

                val randomAction = actions.random()
                logger.debug { "Village Elder performing action: $randomAction" }
                performAction(randomAction)
            }
        }
    }

    /**
     * Elders are contemplative and don't need frequent updates.
     */
    override fun getTickInterval(): Int = 20
}
