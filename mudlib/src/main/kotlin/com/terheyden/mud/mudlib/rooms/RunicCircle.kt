package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.puzzle.PuzzleRoom
import com.terheyden.mud.corelib.puzzle.PuzzleState
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.ElementalCrystal
import com.terheyden.mud.mudlib.items.PowerStone
import com.terheyden.mud.mudlib.puzzles.RunicSequencePuzzle
import org.springframework.stereotype.Component

/**
 * A mystical chamber containing a circle of elemental runes.
 * Players must activate the runes in the correct sequence to unlock the chamber's power.
 */
@Component
class RunicCircle : PuzzleRoom(
    id = "runic_circle",
    name = "Runic Circle",
    description = "You stand in a circular chamber carved from smooth black stone. In the center " +
            "of the room is a perfect circle of five ancient runes, each carved into the floor " +
            "and filled with different colored crystals. The air hums with dormant magical energy, " +
            "and you can feel the power waiting to be awakened. Mystical symbols cover the walls, " +
            "and a soft, ethereal light emanates from cracks in the stone ceiling above.",
    features = mutableListOf(
        RoomFeature(
            id = "earth_rune",
            names = listOf("earth rune", "earth", "brown rune", "mountain rune"),
            description = "A brown rune carved with intricate mountain and stone symbols. " +
                    "The carving is filled with amber crystals that seem to pulse with " +
                    "the steady rhythm of the earth itself.",
            keywords = listOf("brown", "mountain", "stone", "symbols", "amber", "crystals", "pulse", "steady", "rhythm")
        ),
        RoomFeature(
            id = "water_rune",
            names = listOf("water rune", "water", "blue rune", "wave rune"),
            description = "A blue rune carved with flowing wave patterns and water symbols. " +
                    "Sapphire crystals fill the grooves, and they seem to shimmer like " +
                    "sunlight on a calm lake.",
            keywords = listOf(
                "blue",
                "flowing",
                "wave",
                "patterns",
                "sapphire",
                "grooves",
                "shimmer",
                "sunlight",
                "calm",
                "lake"
            )
        ),
        RoomFeature(
            id = "fire_rune",
            names = listOf("fire rune", "fire", "red rune", "flame rune"),
            description = "A red rune carved with dancing flame motifs and solar symbols. " +
                    "Ruby crystals fill the design, glowing with an inner warmth that " +
                    "makes the air above it shimmer with heat.",
            keywords = listOf(
                "red",
                "dancing",
                "flame",
                "motifs",
                "solar",
                "ruby",
                "glowing",
                "inner",
                "warmth",
                "shimmer",
                "heat"
            )
        ),
        RoomFeature(
            id = "air_rune",
            names = listOf("air rune", "air", "white rune", "wind rune"),
            description = "A white rune carved with spiraling wind patterns and cloud symbols. " +
                    "Clear quartz crystals fill the carving, and a gentle breeze seems to " +
                    "emanate from the rune even in this enclosed space.",
            keywords = listOf(
                "white",
                "spiraling",
                "wind",
                "patterns",
                "cloud",
                "quartz",
                "gentle",
                "breeze",
                "emanate",
                "enclosed"
            )
        ),
        RoomFeature(
            id = "spirit_rune",
            names = listOf("spirit rune", "spirit", "purple rune", "mystical rune"),
            description = "A purple rune carved with complex mystical symbols and arcane patterns. " +
                    "Amethyst crystals fill the intricate design, and the entire rune seems to " +
                    "pulse with otherworldly energy.",
            keywords = listOf(
                "purple",
                "complex",
                "mystical",
                "arcane",
                "patterns",
                "amethyst",
                "intricate",
                "pulse",
                "otherworldly",
                "energy"
            )
        ),
        RoomFeature(
            id = "runic_circle",
            names = listOf("runic circle", "circle", "runes", "magical circle"),
            description = "Five ancient runes are arranged in a perfect circle on the floor. Each " +
                    "rune represents a different element and glows with its own unique energy. " +
                    "The circle itself seems to be a focus for magical power, waiting for the " +
                    "correct sequence to be activated.",
            keywords = listOf(
                "five",
                "ancient",
                "arranged",
                "perfect",
                "represents",
                "element",
                "unique",
                "focus",
                "magical",
                "power",
                "sequence",
                "activated"
            )
        ),
        RoomFeature(
            id = "mystical_symbols",
            names = listOf("mystical symbols", "symbols", "wall symbols", "carvings"),
            description = "The walls are covered with intricate mystical symbols and arcane writings. " +
                    "Some appear to be instructions or hints about the proper order of elemental " +
                    "activation, though the meaning is not immediately clear.",
            keywords = listOf(
                "covered",
                "intricate",
                "arcane",
                "writings",
                "instructions",
                "hints",
                "proper",
                "order",
                "elemental",
                "activation",
                "meaning",
                "clear"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to AncientLibrary::class,
        Direction.NORTH to ElementalShrine::class,
    ),
    items = mutableListOf(
        // Items will be revealed when puzzle is solved
    ),
) {

    private val runicPuzzle = RunicSequencePuzzle()

    override fun handlePuzzleInteraction(playerId: String, action: String, target: String): String? {
        return when (action.lowercase()) {
            "touch", "activate", "use" -> handleRuneActivation(target)
            "examine" -> handleRuneExamination(target)
            else -> null
        }
    }

    private fun handleRuneActivation(target: String): String {
        if (puzzleState == PuzzleState.SOLVED) {
            return "The runic circle has already been activated. Its power flows freely through the chamber."
        }

        val runeName = extractRuneName(target)
        if (runeName == null) {
            return "You must specify which rune to activate. The available runes are: earth, water, fire, air, and spirit."
        }

        val result = runicPuzzle.activateRune(runeName)

        return when (result) {
            RunicSequencePuzzle.ActivationResult.CORRECT_STEP -> {
                puzzleState = PuzzleState.IN_PROGRESS
                "The ${runeName} rune flares to life with brilliant energy! The sequence continues... " +
                        "You have activated ${runicPuzzle.getCurrentSequence().size} of 5 runes correctly."
            }

            RunicSequencePuzzle.ActivationResult.SEQUENCE_COMPLETE -> {
                onPuzzleSolved("player")
                revealTreasures()
                "The ${runeName} rune blazes with power, completing the sequence! All five runes " +
                        "now glow in harmony, and the chamber fills with swirling elemental energy. " +
                        "A hidden alcove opens in the wall, revealing ancient treasures!"
            }

            RunicSequencePuzzle.ActivationResult.WRONG_SEQUENCE -> {
                "The ${runeName} rune flickers briefly, then dims. That was not the correct sequence! " +
                        "All runes have reset. You have ${runicPuzzle.getRemainingAttempts()} attempts remaining. " +
                        "Try examining the wall symbols for guidance."
            }

            RunicSequencePuzzle.ActivationResult.FAILED -> {
                onPuzzleFailed("player")
                runicPuzzle.reset()
                "The runes flash angrily and then go completely dark. You have failed to unlock " +
                        "the sequence. The puzzle has reset - you may try again."
            }

            RunicSequencePuzzle.ActivationResult.INVALID_RUNE -> {
                "There is no rune by that name. The available runes are: earth, water, fire, air, and spirit."
            }

            RunicSequencePuzzle.ActivationResult.ALREADY_ACTIVATED -> {
                "The ${runeName} rune is already activated in the current sequence."
            }
        }
    }

    private fun handleRuneExamination(target: String): String? {
        val runeName = extractRuneName(target)
        if (runeName != null) {
            return runicPuzzle.getRuneDescription(runeName)
        }

        if (target.contains("symbol") || target.contains("wall")) {
            return getSymbolHint()
        }

        return null
    }

    private fun extractRuneName(target: String): String? {
        val normalized = target.lowercase()
        return when {
            normalized.contains("earth") -> "earth"
            normalized.contains("water") -> "water"
            normalized.contains("fire") -> "fire"
            normalized.contains("air") -> "air"
            normalized.contains("spirit") -> "spirit"
            else -> null
        }
    }

    private fun getSymbolHint(): String {
        val hint = runicPuzzle.getHint()
        return if (hint != null) {
            "Among the mystical symbols on the wall, you notice one that seems to glow faintly: " +
                    "\"$hint\""
        } else {
            "The symbols on the wall seem to shift and change, but their meaning remains unclear."
        }
    }

    private fun revealTreasures() {
        addItem(ElementalCrystal())
        addItem(PowerStone())
    }

    override fun getSolvedDescription(): String {
        return "The runic circle blazes with elemental power, all five runes glowing in perfect " +
                "harmony. Swirling energies of earth, water, fire, air, and spirit dance through " +
                "the chamber, and a hidden alcove has opened to reveal ancient treasures."
    }

    override fun resetPuzzle() {
        super.resetPuzzle()
        runicPuzzle.reset()
        removeItem("elemental_crystal")
        removeItem("power_stone")
    }

    override fun onPlayerEnter(playerId: String) {
        if (puzzleState == PuzzleState.UNSOLVED) {
            // Could provide initial guidance about the runes
        }
    }
}
