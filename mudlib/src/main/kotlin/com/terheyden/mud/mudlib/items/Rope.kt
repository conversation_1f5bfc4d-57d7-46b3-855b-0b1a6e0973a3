package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A sturdy rope useful for climbing and securing items.
 */
class Rope : Item(
    id = "rope",
    name = "hemp rope",
    description = "A coil of strong hemp rope, about fifty feet in length. The rope is well-made " +
            "with tight, even braiding that gives it excellent strength and durability. It's " +
            "the kind of rope that could support the weight of a person climbing or be used " +
            "to secure heavy loads. The hemp has a natural brown color and rough texture.",
    aliases = listOf("rope", "hemp", "coil", "climbing rope"),
    weight = 8,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the rope carefully, testing its strength and checking for any weak " +
                "spots. The hemp fibers are tightly woven and the rope feels solid and reliable. " +
                "This would be perfect for climbing down cliffs, securing loads, or even tying " +
                "up prisoners if the need arose. A good rope is an adventurer's best friend."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The rope is coiled neatly and ready for use. It looks strong enough to support " +
                "considerable weight and would be invaluable for climbing or securing items."
    }
}
