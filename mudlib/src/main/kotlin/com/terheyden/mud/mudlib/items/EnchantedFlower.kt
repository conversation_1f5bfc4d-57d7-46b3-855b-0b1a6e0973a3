package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A magical flower blessed by forest spirits.
 */
class EnchantedFlower : Item(
    id = "enchanted_flower",
    name = "enchanted flower",
    description = "A beautiful flower that seems to glow with inner light. Its petals shift " +
            "through colors that don't exist in nature - deep purples that fade to silver, " +
            "blues that sparkle like starlight, and golds that warm like sunlight. The " +
            "flower never wilts and emanates a sense of peace and natural magic.",
    aliases = listOf("flower", "magical flower", "glowing flower", "blessed flower"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This flower was blessed by a forest spirit and carries powerful natural magic.")
            appendLine("It might provide protection or enhancement when carried.")
        }
    }
}
