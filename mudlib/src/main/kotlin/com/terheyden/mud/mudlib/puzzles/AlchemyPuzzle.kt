package com.terheyden.mud.mudlib.puzzles

/**
 * An alchemy puzzle where players must mix ingredients in the correct order and proportions.
 */
class AlchemyPuzzle {

    // Available ingredients
    private val availableIngredients = mapOf(
        "moonflower" to Ingredient("Moonflower Petals", "Silvery petals that glow softly in darkness", 3),
        "dragon_scale" to Ingredient("Dragon Scale", "A shimmering red scale that radiates heat", 1),
        "spring_water" to Ingredient("Spring Water", "Crystal clear water from a sacred spring", 5),
        "phoenix_feather" to Ingredient("Phoenix Feather", "A golden feather that never burns", 1),
        "nightshade" to Ingredient("Nightshade Extract", "A dark purple liquid with mysterious properties", 2)
    )

    // The correct recipe for the Elixir of Wisdom
    private val correctRecipe = listOf(
        RecipeStep("spring_water", 2, "Add spring water as the base"),
        RecipeStep("moonflower", 1, "Add moonflower petals for clarity"),
        RecipeStep("dragon_scale", 1, "Add dragon scale for power"),
        RecipeStep("nightshade", 1, "Add nightshade extract for mystery"),
        RecipeStep("phoenix_feather", 1, "Add phoenix feather for transformation")
    )

    // Current state of the cauldron
    private val cauldronContents = mutableMapOf<String, Int>()
    private var currentStep = 0
    private var isComplete = false
    private var hasFailed = false

    data class Ingredient(
        val displayName: String,
        val description: String,
        val availableQuantity: Int,
    )

    data class RecipeStep(
        val ingredientId: String,
        val quantity: Int,
        val instruction: String,
    )

    /**
     * Add an ingredient to the cauldron.
     */
    fun addIngredient(ingredientId: String, quantity: Int = 1): AlchemyResult {
        if (isComplete) return AlchemyResult.ALREADY_COMPLETE
        if (hasFailed) return AlchemyResult.MIXTURE_RUINED

        val ingredient = availableIngredients[ingredientId] ?: return AlchemyResult.INVALID_INGREDIENT

        if (quantity <= 0) return AlchemyResult.INVALID_QUANTITY
        if (quantity > ingredient.availableQuantity) return AlchemyResult.INSUFFICIENT_INGREDIENT

        // Check if this matches the current step in the recipe
        if (currentStep >= correctRecipe.size) {
            hasFailed = true
            return AlchemyResult.TOO_MANY_INGREDIENTS
        }

        val expectedStep = correctRecipe[currentStep]

        if (ingredientId != expectedStep.ingredientId) {
            hasFailed = true
            return AlchemyResult.WRONG_INGREDIENT
        }

        if (quantity != expectedStep.quantity) {
            hasFailed = true
            return AlchemyResult.WRONG_QUANTITY
        }

        // Correct ingredient and quantity!
        cauldronContents[ingredientId] = cauldronContents.getOrDefault(ingredientId, 0) + quantity
        currentStep++

        if (currentStep >= correctRecipe.size) {
            isComplete = true
            return AlchemyResult.RECIPE_COMPLETE
        }

        return AlchemyResult.STEP_CORRECT
    }

    /**
     * Get the current hint for the next step.
     */
    fun getCurrentHint(): String? {
        if (isComplete || hasFailed || currentStep >= correctRecipe.size) return null
        return correctRecipe[currentStep].instruction
    }

    /**
     * Get the description of an ingredient.
     */
    fun getIngredientDescription(ingredientId: String): String? {
        val ingredient = availableIngredients[ingredientId] ?: return null
        return "${ingredient.displayName}: ${ingredient.description} (Available: ${ingredient.availableQuantity})"
    }

    /**
     * Get all available ingredients.
     */
    fun getAvailableIngredients(): Map<String, Ingredient> = availableIngredients

    /**
     * Get the current contents of the cauldron.
     */
    fun getCauldronContents(): Map<String, Int> = cauldronContents.toMap()

    /**
     * Get a description of what's currently in the cauldron.
     */
    fun getCauldronDescription(): String {
        if (cauldronContents.isEmpty()) {
            return "The cauldron is empty, waiting for ingredients."
        }

        val contents = cauldronContents.entries.joinToString(", ") { (id, quantity) ->
            val ingredient = availableIngredients[id]!!
            "$quantity ${ingredient.displayName.lowercase()}"
        }

        return when {
            isComplete -> "The cauldron contains a perfect Elixir of Wisdom, glowing with golden light: $contents"
            hasFailed -> "The cauldron contains a ruined mixture that bubbles ominously: $contents"
            else -> "The cauldron contains: $contents"
        }
    }

    /**
     * Reset the puzzle.
     */
    fun reset() {
        cauldronContents.clear()
        currentStep = 0
        isComplete = false
        hasFailed = false
    }

    /**
     * Check if the recipe is complete.
     */
    fun isRecipeComplete(): Boolean = isComplete

    /**
     * Check if the mixture has failed.
     */
    fun hasMixtureFailed(): Boolean = hasFailed

    /**
     * Get the current step number.
     */
    fun getCurrentStep(): Int = currentStep

    /**
     * Get the total number of steps.
     */
    fun getTotalSteps(): Int = correctRecipe.size

    enum class AlchemyResult {
        STEP_CORRECT,
        RECIPE_COMPLETE,
        WRONG_INGREDIENT,
        WRONG_QUANTITY,
        TOO_MANY_INGREDIENTS,
        INVALID_INGREDIENT,
        INVALID_QUANTITY,
        INSUFFICIENT_INGREDIENT,
        MIXTURE_RUINED,
        ALREADY_COMPLETE
    }
}
