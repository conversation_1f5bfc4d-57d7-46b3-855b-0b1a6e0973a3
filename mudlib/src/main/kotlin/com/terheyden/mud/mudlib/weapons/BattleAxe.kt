package com.terheyden.mud.mudlib.weapons

import com.terheyden.mud.corelib.weapon.Weapon
import com.terheyden.mud.corelib.weapon.WeaponType

/**
 * A heavy battle axe favored by warriors.
 */
class BattleAxe : Weapon(
    id = "battle_axe",
    name = "battle axe",
    description = "A formidable two-handed battle axe with a massive steel head mounted on a " +
            "sturdy oak handle. The blade is sharp and well-maintained, with intricate " +
            "engravings of battle scenes etched into the metal. The weapon feels heavy " +
            "and powerful, designed for devastating attacks.",
    aliases = listOf("axe", "battle axe", "two-handed axe", "steel axe"),
    weight = 8,
    damage = 20,
    weaponType = WeaponType.MELEE,
    durability = 120,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The engravings depict epic battles between warriors and monsters.")
            appendLine("This weapon requires significant strength to wield effectively.")
        }
    }
}
