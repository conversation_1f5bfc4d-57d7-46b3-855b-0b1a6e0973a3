package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.Rope

class DockWorker : NPC(
    id = "dock_worker",
    name = "dock worker",
    description = "A strong dock worker who loads and unloads cargo from boats.",
    inventory = mutableListOf(Rope(), GoldCoins()),
    maxHealthPoints = 100,
    currentHealthPoints = 100,
    level = 2,
    baseAttackPower = 10,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The dock worker wipes sweat from his brow. 'Busy day at the docks! Can I help you with something?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The dock worker nods. 'Always something to load or unload around here!'"
    }
}
