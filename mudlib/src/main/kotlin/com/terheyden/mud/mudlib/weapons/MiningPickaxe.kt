package com.terheyden.mud.mudlib.weapons

import com.terheyden.mud.corelib.weapon.Weapon
import com.terheyden.mud.corelib.weapon.WeaponType

/**
 * A mining pickaxe that can be used as an improvised weapon.
 */
class MiningPickaxe : Weapon(
    id = "mining_pickaxe",
    name = "mining pickaxe",
    description = "A heavy mining pickaxe with a steel head and wooden handle, designed for " +
            "breaking through rock and ore. While not intended as a weapon, its sharp point " +
            "and heavy weight make it surprisingly effective in combat. The handle shows " +
            "wear from years of use in the mines.",
    aliases = listOf("pickaxe", "pick", "mining pick", "tool"),
    weight = 6,
    damage = 12,
    weaponType = WeaponType.IMPROVISED,
    durability = 80,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The steel head is designed for piercing rock, making it effective against armor.")
            appendLine("This tool could also be used for mining operations.")
        }
    }
}
