package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.GlowingCrystal
import com.terheyden.mud.mudlib.npcs.FierceWolf
import org.springframework.stereotype.Component

/**
 * A magnificent cave filled with glowing crystals.
 */
@Component
class CrystalCave : Room(
    id = "crystal_cave",
    name = "Crystal Cave",
    description = "You have entered a _magnificent cave_ filled with *glowing crystals*. " +
            "The walls sparkle with an `ethereal blue light`, and you can hear the gentle sound " +
            "of water dripping somewhere in the depths. The air feels charged with _magical energy_.",
    features = mutableListOf(
        RoomFeature(
            id = "cave_walls",
            names = listOf("walls", "cave walls", "crystal walls"),
            description = "The cave walls are embedded with countless crystals of various sizes. They range " +
                    "from tiny gems no bigger than your fingernail to massive formations that jut out " +
                    "several feet from the wall. Each crystal pulses with its own rhythm of blue light.",
            keywords = listOf("crystals", "gems", "formations", "blue", "light", "pulse", "rhythm")
        ),
        RoomFeature(
            id = "glowing_crystals_walls",
            names = listOf("crystals", "glowing crystals", "blue crystals"),
            description = "The crystals embedded in the walls create a mesmerizing display of blue light. " +
                    "Some crystals are clear as glass, others are deep blue like sapphires. The light " +
                    "seems to flow from crystal to crystal in slow, hypnotic waves.",
            keywords = listOf("blue", "sapphires", "glass", "waves", "hypnotic", "mesmerizing", "flow")
        ),
        RoomFeature(
            id = "water_dripping",
            names = listOf("water", "dripping", "water dripping", "drops"),
            description = "You can hear the gentle sound of water dripping somewhere deeper in the cave. " +
                    "The sound echoes softly off the crystal walls, creating a peaceful, rhythmic melody. " +
                    "Occasionally, you see a drop of water catch the crystal light as it falls.",
            keywords = listOf("sound", "echo", "melody", "peaceful", "rhythmic", "drop", "falls")
        ),
        RoomFeature(
            id = "cave_depths",
            names = listOf("depths", "deeper cave", "back of cave"),
            description = "The cave extends further back into darkness, beyond where the crystal light reaches. " +
                    "You can sense vast spaces in the depths, and the air currents suggest there may be " +
                    "other chambers or passages hidden in the shadows.",
            keywords = listOf("darkness", "spaces", "chambers", "passages", "shadows", "hidden", "air")
        ),
        RoomFeature(
            id = "magical_energy",
            names = listOf("magical energy", "energy", "magic", "charged air"),
            description = "The air itself seems to thrum with magical energy. You can feel it tingling on " +
                    "your skin and making the hair on your arms stand up. The energy seems to emanate " +
                    "from the crystals themselves, creating an almost electric atmosphere.",
            keywords = listOf("thrum", "tingling", "skin", "hair", "electric", "atmosphere", "emanate")
        ),
    ),
    exits = mutableMapOf(
        Direction.WEST to ForestClearing::class,
    ),
    items = mutableListOf(
        GlowingCrystal(),
        FierceWolf(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The magical energy could have effects on players
        // Could restore mana, provide temporary buffs, or trigger magical events
    }
}
