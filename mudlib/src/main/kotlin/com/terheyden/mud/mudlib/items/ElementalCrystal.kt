package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A powerful crystal infused with elemental energies.
 */
class ElementalCrystal : Item(
    id = "elemental_crystal",
    name = "elemental crystal",
    description = "A magnificent crystal that pulses with the combined energies of all five elements. " +
            "Earth, water, fire, air, and spirit swirl within its translucent depths, creating " +
            "mesmerizing patterns of light and color. The crystal feels warm to the touch and " +
            "hums with barely contained power.",
    aliases = listOf("crystal", "elemental", "power crystal", "magical crystal"),
    weight = 3,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You hold the elemental crystal aloft and feel its power flow through you. " +
                "For a brief moment, you sense the harmony of all elements and feel your " +
                "understanding of magic deepen. The crystal's glow intensifies, then fades " +
                "back to its normal radiance."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The crystal seems to respond to your presence, its internal energies swirling " +
                "faster as you observe it. You sense that this could be a powerful focus for " +
                "magical energies, or perhaps a key component in advanced alchemical work."
    }
}
