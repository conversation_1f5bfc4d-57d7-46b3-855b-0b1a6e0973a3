package com.terheyden.mud.mudlib.doors

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Door

class GardenDoor(
    direction: Direction,
) : Door(
    id = "garden_door",
    name = "garden door",
    aliases = listOf("door", "wooden door", "hidden door"),
    description = "Almost hidden among the thick oak trees to the west, you notice an old wooden door " +
            "set into what appears to be a natural stone archway covered in vines. The door is made " +
            "of dark, weathered wood and is secured with an ancient iron lock. Strange symbols are " +
            "carved around the doorframe, and the whole entrance has an air of mystery and magic.",
    direction = direction,
    locked = true,
    requiredKeyId = "rusty_key",
)
