package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A small crystal shard that emits magical light.
 */
class GlowingCrystal : Item(
    id = "glowing_crystal",
    name = "glowing crystal",
    description = "A small crystal shard that emits a soft blue light. " +
            "It feels slightly warm and tingles when touched.",
    weight = 3,
    aliases = listOf("crystal", "shard", "blue crystal", "crystal shard", "gem")
) {

    /**
     * Use the crystal as a light source.
     */
    fun illuminate(): String {
        return "The crystal flares with brilliant blue light, illuminating the area around you. " +
                "The magical glow reveals details that were hidden in shadow."
    }

    /**
     * Use the crystal to enhance magical abilities.
     */
    fun channelEnergy(): String {
        return "You focus your energy through the crystal. It pulses with power, " +
                "amplifying your magical abilities and making you feel more attuned to mystical forces."
    }

    /**
     * Check if the crystal resonates with other magical items.
     */
    fun resonatesWith(otherItem: Item): <PERSON><PERSON>an {
        return when (otherItem.id) {
            "crystal_orb" -> true
            "ancient_scroll" -> true
            else -> false
        }
    }

    /**
     * Custom examine description that emphasizes its magical properties.
     */
    override fun getExamineDescription(): String {
        return super.getExamineDescription() + " The crystal seems to pulse in rhythm with your heartbeat, " +
                "and you can sense the raw magical energy contained within its crystalline structure."
    }
}
