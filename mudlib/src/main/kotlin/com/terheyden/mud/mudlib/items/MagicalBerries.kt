package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * Magical berries that provide healing and temporary magical enhancement.
 */
class MagicalBerries : Item(
    id = "magical_berries",
    name = "cluster of magical berries",
    description = "A small cluster of berries that glow with an inner light, shifting through " +
            "colors of deep purple, electric blue, and shimmering gold. They grow on a " +
            "delicate silver vine and emit a sweet, intoxicating fragrance. The berries " +
            "pulse gently with magical energy, as if they contain concentrated starlight.",
    aliases = listOf("berries", "magical berries", "glowing berries", "cluster"),
    weight = 1,
    isPickupable = true,
), Useable {

    private var berriesRemaining = 3

    /**
     * Eat one of the magical berries.
     */
    fun eat(): String {
        return if (berriesRemaining > 0) {
            berriesRemaining--
            when (berriesRemaining) {
                2 -> "You pluck one of the glowing berries and eat it. It tastes like liquid " +
                        "starlight and honey, filling you with warmth and energy. You feel " +
                        "significantly healed and your magical abilities seem enhanced!"

                1 -> "The second berry is even more potent, its magical essence flowing through " +
                        "your body like liquid fire. Your wounds close and your mind feels " +
                        "sharper, more attuned to magical forces."

                0 -> "You eat the final berry, and its power is overwhelming. Pure magical " +
                        "energy courses through your veins, completely restoring your health " +
                        "and granting you temporary magical sight. The empty vine crumbles " +
                        "to silver dust in your hands."

                else -> "There are no more berries to eat."
            }
        } else {
            "There are no berries left - only silver dust remains."
        }
    }

    /**
     * Check how many berries remain.
     */
    fun getBerriesRemaining(): Int = berriesRemaining

    /**
     * Check if there are berries left to eat.
     */
    fun hasBerriesLeft(): Boolean = berriesRemaining > 0

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            when (berriesRemaining) {
                3 -> appendLine("The cluster holds three perfect berries, each pulsing with magical light.")
                2 -> appendLine("Two berries remain on the silver vine, their glow slightly dimmed.")
                1 -> appendLine("Only one berry remains, but it glows more brightly than before.")
                0 -> appendLine("Only silver dust remains where the magical berries once grew.")
            }
            if (hasBerriesLeft()) {
                appendLine("You could try to 'eat' one of these magical berries.")
            }
        }
    }

    override fun getShortDescription(): String {
        return when (berriesRemaining) {
            3 -> name
            2 -> "cluster with two magical berries"
            1 -> "single magical berry"
            0 -> "silver dust from magical berries"
            else -> name
        }
    }

    override fun use(user: Player): String {
        if (hasBerriesLeft()) {
            val result = eat()
            // Heal the player
            val healAmount = when (getBerriesRemaining()) {
                2 -> 30 // First berry
                1 -> 40 // Second berry
                0 -> 50 // Final berry
                else -> 0
            }
            if (healAmount > 0) {
                user.heal(healAmount)
            }
            return result
        } else {
            return "There are no berries left to eat."
        }
    }
}
