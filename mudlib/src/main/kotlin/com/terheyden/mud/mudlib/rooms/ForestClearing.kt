package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.corelib.room.StartingRoom
import com.terheyden.mud.mudlib.doors.GardenDoor
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.WoodenStick
import com.terheyden.mud.mudlib.npcs.WiseMerchant
import org.springframework.stereotype.Component

/**
 * The peaceful forest clearing where players begin their adventure.
 */
@Component
class ForestClearing : Room(
    id = "forest_clearing",
    name = "Forest Clearing",
    description = "You stand in a peaceful clearing surrounded by tall oak trees. " +
            "Sunlight filters through the canopy, creating patterns on the forest floor. " +
            "A worn path leads north toward an old stone tower.",
    features = mutableListOf(
        RoomFeature(
            id = "oak_trees",
            names = listOf("oak trees", "trees", "oaks"),
            description = "The ancient oak trees tower above you, their thick trunks scarred by age and weather. " +
                    "Their branches interweave to form a natural canopy that filters the sunlight into " +
                    "dancing patterns on the forest floor.",
            keywords = listOf("tree", "oak", "trunk", "trunks", "branches")
        ),
        RoomFeature(
            id = "canopy",
            names = listOf("canopy", "branches", "leaves"),
            description = "The canopy above forms a natural ceiling of interwoven branches and leaves. " +
                    "Sunlight filters through in golden shafts, creating an ever-changing pattern of " +
                    "light and shadow on the ground below.",
            keywords = listOf("ceiling", "sunlight", "light", "shadow", "patterns")
        ),
        RoomFeature(
            id = "stone_tower_distant",
            names = listOf("stone tower", "tower", "old tower"),
            description = "Through the trees to the north, you can see the imposing silhouette of an ancient " +
                    "stone tower. Its weathered walls rise high above the forest canopy, and you can " +
                    "make out strange architectural details even from this distance.",
            keywords = listOf("stone", "ancient", "silhouette", "north")
        ),
    ),
    exits = mutableMapOf(
        Direction.NORTH to TowerEntrance::class,
        Direction.SOUTH to ForestPath::class,
        Direction.EAST to CrystalCave::class,
        Direction.WEST to SecretGarden::class,
    ),
    items = mutableListOf(
        WoodenStick(),
        HealingPotion(),
        WiseMerchant(),
    ),
    doors = mutableListOf(GardenDoor(Direction.WEST)),
), StartingRoom {

    override fun onPlayerEnter(playerId: String) {
        // Could add special behavior when players enter, like:
        // - Random encounters
        // - Weather effects
        // - Time-based events
    }
}
