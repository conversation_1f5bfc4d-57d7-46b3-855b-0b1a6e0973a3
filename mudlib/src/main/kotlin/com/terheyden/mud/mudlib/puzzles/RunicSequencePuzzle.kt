package com.terheyden.mud.mudlib.puzzles

/**
 * A puzzle involving activating runic symbols in the correct sequence.
 * Players must touch or activate runes in a specific order to unlock the chamber.
 */
class RunicSequencePuzzle {

    // The correct sequence of runes to activate
    private val correctSequence = listOf("earth", "water", "fire", "air", "spirit")

    // The current sequence the player has activated
    private val currentSequence = mutableListOf<String>()

    // Available runes that can be activated
    private val availableRunes = mapOf(
        "earth" to RuneInfo("Earth", "A brown rune carved with mountain symbols", false),
        "water" to RuneInfo("Water", "A blue rune carved with wave patterns", false),
        "fire" to RuneInfo("Fire", "A red rune carved with flame motifs", false),
        "air" to RuneInfo("Air", "A white rune carved with wind spirals", false),
        "spirit" to RuneInfo("Spirit", "A purple rune carved with mystical symbols", false)
    )

    private var maxAttempts = 3
    private var attemptsUsed = 0

    data class RuneInfo(
        val displayName: String,
        val description: String,
        var isActivated: Boolean,
    )

    /**
     * Attempt to activate a rune by name.
     */
    fun activateRune(runeName: String): ActivationResult {
        val normalizedName = runeName.lowercase().trim()

        if (!availableRunes.containsKey(normalizedName)) {
            return ActivationResult.INVALID_RUNE
        }

        val rune = availableRunes[normalizedName]!!

        if (rune.isActivated) {
            return ActivationResult.ALREADY_ACTIVATED
        }

        // Add to current sequence
        currentSequence.add(normalizedName)
        rune.isActivated = true

        // Check if this matches the correct sequence so far
        if (currentSequence.size <= correctSequence.size &&
            currentSequence[currentSequence.size - 1] == correctSequence[currentSequence.size - 1]
        ) {

            // Check if sequence is complete
            return if (currentSequence.size == correctSequence.size) {
                ActivationResult.SEQUENCE_COMPLETE
            } else {
                ActivationResult.CORRECT_STEP
            }
        } else {
            // Wrong rune activated
            attemptsUsed++
            return if (attemptsUsed >= maxAttempts) {
                ActivationResult.FAILED
            } else {
                resetSequence()
                ActivationResult.WRONG_SEQUENCE
            }
        }
    }

    /**
     * Reset the current sequence and rune states.
     */
    fun resetSequence() {
        currentSequence.clear()
        availableRunes.values.forEach { it.isActivated = false }
    }

    /**
     * Get the description of a specific rune.
     */
    fun getRuneDescription(runeName: String): String? {
        val normalizedName = runeName.lowercase().trim()
        val rune = availableRunes[normalizedName] ?: return null

        val activationStatus = if (rune.isActivated) " (glowing with activated energy)" else " (dormant)"
        return rune.description + activationStatus
    }

    /** Get a list of all available runes. */
    fun getAllRunes(): Map<String, RuneInfo> = availableRunes

    /** Get the current sequence progress. */
    fun getCurrentSequence(): List<String> = currentSequence.toList()

    /** Get remaining attempts. */
    fun getRemainingAttempts(): Int = maxAttempts - attemptsUsed

    /** Check if the puzzle is complete. */
    fun isComplete(): Boolean = currentSequence == correctSequence

    /** Get a hint about the next rune to activate. */
    fun getHint(): String? {
        if (currentSequence.size >= correctSequence.size) return null

        val nextRune = correctSequence[currentSequence.size]
        return when (nextRune) {
            "earth" -> "The foundation of all things, solid and enduring."
            "water" -> "That which flows and adapts, essential for life."
            "fire" -> "The force of transformation and energy."
            "air" -> "The breath of life, invisible yet vital."
            "spirit" -> "The essence that binds all elements together."
            else -> null
        }
    }

    /** Reset the entire puzzle. */
    fun reset() {
        resetSequence()
        attemptsUsed = 0
    }

    enum class ActivationResult {
        CORRECT_STEP,
        SEQUENCE_COMPLETE,
        WRONG_SEQUENCE,
        FAILED,
        INVALID_RUNE,
        ALREADY_ACTIVATED
    }
}
