package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A sturdy travel pack for carrying adventuring gear.
 */
class TravelPack : Item(
    id = "travel_pack",
    name = "travel pack",
    description = "A well-crafted leather backpack designed for long journeys and rough use. " +
            "The pack has multiple compartments and pockets for organizing gear, and the " +
            "straps are padded for comfort during extended wear. The leather is thick and " +
            "weather-resistant, with reinforced stitching at stress points. Buckles and " +
            "ties allow for secure closure and external attachment of additional items.",
    aliases = listOf("pack", "backpack", "bag", "leather pack"),
    weight = 5,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the travel pack thoroughly, checking all the compartments and " +
                "testing the straps. The craftsmanship is excellent - the leather is supple " +
                "but strong, the stitching is tight and even, and all the buckles work smoothly. " +
                "This pack could carry a substantial amount of gear while distributing the " +
                "weight comfortably across your shoulders and back."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The pack has a main compartment large enough for clothes and supplies, plus " +
                "several smaller pockets for organizing tools, coins, and other small items. " +
                "External loops and ties allow for attaching bedrolls, rope, or other bulky gear."
    }
}
