package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.Flour
import com.terheyden.mud.mudlib.items.SweetRoll

class BakersApprentice : NPC(
    id = "bakers_apprentice",
    name = "baker's apprentice",
    description = "A young apprentice learning the art of baking.",
    inventory = mutableListOf(SweetRoll(), Flour()),
    maxHealthPoints = 65,
    currentHealthPoints = 65,
    level = 1,
    baseAttackPower = 4,
    baseDefense = 5,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The young apprentice smiles shyly. 'Hello! I'm still learning to bake like <PERSON>.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The apprentice nods eagerly. '<PERSON> is teaching me everything about baking!'"
    }
}
