package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A pouch of gold coins - valuable currency.
 */
class GoldCoins : Item(
    id = "gold_coins",
    name = "pouch of gold coins",
    description = "A small leather pouch filled with gleaming gold coins. The coins bear the " +
            "mark of various kingdoms and eras, suggesting they've been collected over time. " +
            "They clink musically when the pouch is moved, and their weight feels substantial " +
            "in your hand.",
    aliases = listOf("coins", "gold", "pouch", "money", "currency"),
    weight = 3,
    isPickupable = true,
) {

    private val coinCount = (10..50).random()

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The pouch contains approximately $coinCount gold coins.")
            appendLine("These could be valuable for trading with merchants.")
        }
    }

    fun getCoinCount(): Int = coinCount
}
