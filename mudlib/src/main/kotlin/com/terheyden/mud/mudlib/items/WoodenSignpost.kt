package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A map of the land.
 */
class WoodenSignpost : Item(
    id = "signpost",
    name = "wooden signpost",
    description = "A wooden signpost with a map of the land.",
    aliases = listOf("signpost", "map", "wooden signpost"),
    weight = 5,
    isPickupable = false,
) {

    /**
     * Read the tome to gain magical knowledge.
     */
    fun read() = """
                                               Dungeon  -  Abandoned
                                               Chamber      Mine
                                                  |
                                              Underground
                                                Tunnel
                              Tower Chamber       |
                                    /           Tower
                              Tower Entrance \ Basement
                                    |
        Enchanted - Secret - Forest Clearing - Crystal
          Grove     Garden     (START)          Cave
                                    |
                              Village Square
                                    |
                              Village Inn
    """.trimIndent()
}
