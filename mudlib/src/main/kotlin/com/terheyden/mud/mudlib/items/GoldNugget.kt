package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A valuable gold nugget found in the mines.
 */
class GoldNugget : Item(
    id = "gold_nugget",
    name = "gold nugget",
    description = "A rough chunk of pure gold, still embedded with bits of quartz and rock " +
            "from where it was mined. The gold gleams with a rich, warm luster that " +
            "catches the light beautifully. This nugget is quite substantial and would " +
            "be worth a considerable sum to the right buyer.",
    aliases = listOf("nugget", "gold", "chunk", "ore"),
    weight = 4,
    isPickupable = true,
) {

    private val value = (50..150).random()

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This nugget appears to be worth approximately $value gold pieces.")
            appendLine("A merchant would likely pay well for such pure gold.")
        }
    }

    fun getValue(): Int = value
}
