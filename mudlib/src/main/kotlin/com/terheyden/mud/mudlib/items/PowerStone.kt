package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A stone charged with mystical power from the runic circle.
 */
class PowerStone : Item(
    id = "power_stone",
    name = "power stone",
    description = "A smooth, obsidian-black stone that pulses with inner light. Runic symbols " +
            "appear and disappear across its surface in a slow, hypnotic rhythm. The stone " +
            "feels surprisingly light for its size and radiates a sense of contained energy " +
            "that makes your fingertips tingle when you touch it.",
    aliases = listOf("stone", "black stone", "runic stone", "obsidian stone"),
    weight = 2,
    isPickupable = true
), Useable {

    private var chargesRemaining = 3

    override fun use(user: Player): String {
        return if (chargesRemaining > 0) {
            chargesRemaining--
            "You focus your will through the power stone and feel a surge of mystical energy! " +
                    "The runic symbols flare brightly for a moment, and you feel refreshed and " +
                    "empowered. The stone dims slightly. (${chargesRemaining} charges remaining)"
        } else {
            "The power stone has been drained of its energy. The runic symbols have faded, " +
                    "leaving only smooth obsidian. Perhaps it could be recharged at its source..."
        }
    }

    override fun getExamineDescription(): String {
        val chargeDescription = when (chargesRemaining) {
            3 -> "The stone pulses with full power, its runic symbols bright and active."
            2 -> "The stone still holds considerable power, though slightly dimmed."
            1 -> "The stone's power is waning, its symbols flickering weakly."
            else -> "The stone appears drained, its surface dark and lifeless."
        }
        
        return super.getExamineDescription() + "\n\n$chargeDescription"
    }
}
