package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * An elegant silver ring with mysterious properties.
 */
class SilverRing : Item(
    id = "silver_ring",
    name = "silver ring",
    description = "An elegant ring crafted from pure silver, its surface polished to a mirror " +
            "shine. Delicate engravings of intertwining vines and leaves spiral around the " +
            "band, and a small blue gemstone is set into the top. The ring feels cool to " +
            "the touch and seems to pulse with a faint magical energy.",
    aliases = listOf("ring", "silver", "elegant ring", "magical ring"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("The blue gemstone seems to swirl with inner light when you look closely.")
            appendLine("This ring might have magical properties if worn.")
        }
    }
}
