package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A mug of refreshing ale from the village inn.
 */
class Ale : Item(
    id = "ale",
    name = "mug of ale",
    description = "A large ceramic mug filled with golden ale that foams at the top. The ale " +
            "has a rich, malty aroma and looks refreshing after a long day of adventuring. " +
            "The mug is warm to the touch and decorated with simple blue patterns around the rim.",
    aliases = listOf("ale", "mug", "beer", "drink"),
    weight = 2,
    isPickupable = true,
), Useable {

    private var hasBeenDrunk = false

    fun drink(): String {
        return if (!hasBeenDrunk) {
            hasBeenDrunk = true
            "You drink the ale in long, satisfying gulps. The rich, malty flavor fills your " +
                    "mouth and the alcohol warms your belly. You feel refreshed and your " +
                    "spirits are lifted!"
        } else {
            "The mug is empty - you've already drunk all the ale."
        }
    }

    fun isEmpty(): Boolean = hasBeenDrunk

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            if (isEmpty()) {
                appendLine("The mug is empty, with only a few drops of ale remaining at the bottom.")
            } else {
                appendLine("The ale looks fresh and inviting. You could 'drink' it.")
            }
        }
    }

    override fun getShortDescription(): String {
        return if (isEmpty()) {
            "empty ale mug"
        } else {
            name
        }
    }

    override fun use(user: Player): String {
        if (!isEmpty()) {
            val result = drink()
            // Provide small health boost
            user.heal(5)
            return result
        } else {
            return "The mug is empty."
        }
    }
}
