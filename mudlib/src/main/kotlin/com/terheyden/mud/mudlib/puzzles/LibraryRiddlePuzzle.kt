package com.terheyden.mud.mudlib.puzzles

/**
 * A riddle puzzle for the Ancient Library.
 * Players must answer riddles correctly to unlock the library's secrets.
 */
class LibraryRiddlePuzzle {

    private val riddles = listOf(
        Riddle(
            question = "I have cities, but no houses dwell within. I have mountains, but no trees therein. " +
                    "I have water, but no fish swim free. What am I?",
            answers = listOf("map", "a map", "the map"),
            hint = "Think about something that shows places but contains none of their living things.",
        ),
        Riddle(
            question = "The more you take, the more you leave behind. What am I?",
            answers = listOf("footsteps", "steps", "footprints", "tracks"),
            hint = "Consider what you create as you move through the world.",
        ),
        Riddle(
            question = "I speak without a mouth and hear without ears. " +
                    "I have no body, but come alive with wind. What am I?",
            answers = listOf("echo", "an echo", "the echo"),
            hint = "Think about sounds that repeat in empty spaces.",
        ),
    )

    private var currentRiddleIndex = 0
    private var attemptsRemaining = 3

    data class Riddle(
        val question: String,
        val answers: List<String>,
        val hint: String,
    )

    fun getCurrentRiddle(): Riddle? {
        return if (currentRiddleIndex < riddles.size) {
            riddles[currentRiddleIndex]
        } else {
            null
        }
    }

    fun checkAnswer(answer: String): PuzzleResult {
        val currentRiddle = getCurrentRiddle() ?: return PuzzleResult.ALREADY_COMPLETE

        val normalizedAnswer = answer.trim().lowercase()
        val isCorrect = currentRiddle.answers.any { it.lowercase() == normalizedAnswer }

        if (isCorrect) {
            currentRiddleIndex++
            return if (currentRiddleIndex >= riddles.size) {
                PuzzleResult.COMPLETE
            } else {
                PuzzleResult.CORRECT_CONTINUE
            }
        } else {
            attemptsRemaining--
            return if (attemptsRemaining <= 0) {
                PuzzleResult.FAILED
            } else {
                PuzzleResult.INCORRECT
            }
        }
    }

    fun getHint(): String? {
        return getCurrentRiddle()?.hint
    }

    fun getAttemptsRemaining(): Int = attemptsRemaining

    fun isComplete(): Boolean = currentRiddleIndex >= riddles.size

    fun reset() {
        currentRiddleIndex = 0
        attemptsRemaining = 3
    }

    enum class PuzzleResult {
        CORRECT_CONTINUE,
        COMPLETE,
        INCORRECT,
        FAILED,
        ALREADY_COMPLETE
    }
}
