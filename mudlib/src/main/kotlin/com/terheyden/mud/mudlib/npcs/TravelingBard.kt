package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins

/**
 * A charismatic traveling bard who shares stories and songs.
 */
class TravelingBard : NPC(
    id = "traveling_bard",
    name = "traveling bard",
    description = "A cheerful young woman with bright green eyes and auburn hair tied back with " +
            "colorful ribbons. She wears a travel-worn but well-maintained outfit of green " +
            "and brown, and carries a beautifully crafted lute across her back. Her smile " +
            "is infectious and she seems to radiate joy and wanderlust.",
    inventory = mutableListOf(GoldCoins()),
    maxHealthPoints = 70,
    currentHealthPoints = 70,
    level = 3,
    baseAttackPower = 6,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var songsPerformed = 0
    private var hasSharedNews = false

    override fun getGreeting(player: Player): String {
        return "The bard looks up from tuning her lute and grins widely. 'Well hello there, " +
                "fellow traveler! I'm always delighted to meet someone new. Care to hear " +
                "a song or perhaps some news from the road?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("song") || lowerMessage.contains("music") || lowerMessage.contains("sing") -> {
                songsPerformed++
                when (songsPerformed) {
                    1 -> "The bard strums her lute and begins to sing: 'Oh, the road goes ever " +
                            "onward, through forest, hill, and dale. Where brave adventurers " +
                            "wander, there's always a tale!' Her voice is clear and melodious, " +
                            "lifting your spirits."

                    2 -> "She plays a different tune, this one more haunting: 'In the tower " +
                            "tall and ancient, where the wizard used to dwell, lie secrets " +
                            "dark and magical, and treasures none can tell.' The melody " +
                            "gives you chills."

                    3 -> "The bard performs an upbeat traveling song: 'Pack your bags and " +
                            "lace your boots, the world is wide and free! There's gold " +
                            "and glory waiting for adventurers like thee!' You feel " +
                            "energized and ready for adventure."

                    else -> "The bard smiles apologetically. 'I'm afraid I've shared all my " +
                            "best songs for now. Perhaps after I've traveled more, I'll " +
                            "have new tales to sing!'"
                }
            }

            lowerMessage.contains("news") || lowerMessage.contains("travel") || lowerMessage.contains("road") -> {
                if (!hasSharedNews) {
                    hasSharedNews = true
                    "The bard leans in conspiratorially. 'I've heard interesting rumors on " +
                            "the road. They say the old mine has been glowing at night, and " +
                            "strange creatures have been seen near the ancient burial grounds. " +
                            "Also, merchants in the capital are paying top coin for magical " +
                            "artifacts these days.'"
                } else {
                    "The bard shrugs. 'That's all the news I have for now. I'll gather " +
                            "more stories as I continue my travels!'"
                }
            }

            lowerMessage.contains("story") || lowerMessage.contains("tale") -> {
                val stories = listOf(
                    "I once met a knight who claimed to have seen a dragon sleeping in " +
                            "the mountains. Whether it was true or just the ale talking, " +
                            "I'll never know!",

                    "There's a legend about a magical sword hidden somewhere in these " +
                            "lands. They say it can only be wielded by someone pure of heart.",

                    "I heard tell of a merchant who found a cave full of crystals that " +
                            "sang when the wind blew through them. Beautiful, but eerie!",

                    "An old woman in the last village told me that the forest spirits " +
                            "have been restless lately. Something about an ancient balance " +
                            "being disturbed."
                )
                "The bard's eyes light up. '${stories.random()}'"
            }

            lowerMessage.contains("lute") || lowerMessage.contains("instrument") -> {
                "The bard lovingly pats her lute. 'This beauty has been with me for five " +
                        "years now. Made by a master craftsman in the capital. She's got " +
                        "the sweetest tone you've ever heard!'"
            }

            lowerMessage.contains("adventure") || lowerMessage.contains("quest") -> {
                "The bard's eyes sparkle with excitement. 'Adventure is the spice of life! " +
                        "I may not be much of a fighter, but I've seen wonders that would " +
                        "make your head spin. The world is full of magic and mystery for " +
                        "those brave enough to seek it!'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "The bard waves cheerfully. 'Safe travels, friend! May your path be " +
                        "filled with wonder and your purse with gold. Perhaps we'll " +
                        "meet again on the road!'"
            }

            else -> {
                val responses = listOf(
                    "The bard tilts her head with interest. 'Tell me more!'",
                    "The bard strums a few notes on her lute thoughtfully.",
                    "The bard's eyes twinkle with curiosity and mischief.",
                    "The bard nods encouragingly, clearly enjoying the conversation.",
                    "The bard smiles warmly, always ready to chat with fellow travelers."
                )
                responses.random()
            }
        }
    }
}
