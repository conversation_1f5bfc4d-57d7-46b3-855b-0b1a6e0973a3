package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A blessed candle that provides light and spiritual comfort.
 */
class BlessedCandle : Item(
    id = "blessed_candle",
    name = "blessed candle",
    description = "A white wax candle that has been blessed by the temple priest. The candle " +
            "gives off a soft, warm light that seems to push back darkness more effectively " +
            "than ordinary candles. Holy symbols are carved into the wax, and it carries " +
            "a faint scent of sacred incense. The flame burns steadily and never flickers.",
    aliases = listOf("candle", "blessed", "holy candle", "sacred candle"),
    weight = 1,
    isPickupable = true
), Useable {

    private var isLit = false
    private var burnTimeRemaining = 120 // minutes

    override fun use(user: Player): String {
        return if (!isLit) {
            isLit = true
            "You light the blessed candle and it immediately begins to glow with warm, " +
                    "golden light. The sacred flame seems to push back the darkness around " +
                    "you, and you feel a sense of peace and protection. The candle will " +
                    "burn for hours without diminishing."
        } else {
            "You extinguish the blessed candle. The light fades, but the sense of blessing " +
                    "remains. You can light it again when needed."
        }
    }

    override fun getExamineDescription(): String {
        val statusDescription = if (isLit) {
            "The candle burns with a steady, blessed flame that provides both light and comfort."
        } else {
            "The candle is unlit but ready to provide blessed illumination when needed."
        }
        
        return super.getExamineDescription() + "\n\n$statusDescription"
    }

    override fun getShortDescription(): String {
        return if (isLit) {
            "lit blessed candle"
        } else {
            name
        }
    }
}
