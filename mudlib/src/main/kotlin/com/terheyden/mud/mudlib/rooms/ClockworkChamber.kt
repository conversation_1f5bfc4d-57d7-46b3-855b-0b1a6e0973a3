package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.ClockworkKey
import com.terheyden.mud.mudlib.items.MechanicalDevice
import org.springframework.stereotype.Component

/**
 * A chamber filled with intricate clockwork mechanisms and mechanical puzzles.
 */
@Component
class ClockworkChamber : Room(
    id = "clockwork_chamber",
    name = "Clockwork Chamber",
    description = "You enter a chamber filled with clockwork mechanisms. Gears of various " +
            "sizes turn rhythmically, creating a mechanical symphony. Brass pipes and copper " +
            "tubes snake along the walls. A massive clockwork device stands in the center " +
            "with dials, levers, and rotating components. The air smells of oil and metal.",
    features = mutableListOf(
        RoomFeature(
            id = "clockwork_gears",
            names = listOf("clockwork gears", "gears", "mechanical gears", "turning gears"),
            description = "Hundreds of brass and steel gears of various sizes turn in perfect " +
                    "synchronization. Some are as small as coins, others as large as wagon wheels. " +
                    "The precision of their movement is mesmerizing, and you can see how each " +
                    "gear connects to others in an intricate mechanical web.",
            keywords = listOf(
                "hundreds",
                "brass",
                "steel",
                "various",
                "sizes",
                "perfect",
                "synchronization",
                "small",
                "coins",
                "large",
                "wagon",
                "wheels",
                "precision",
                "mesmerizing",
                "connects",
                "intricate",
                "mechanical",
                "web"
            )
        ),
        RoomFeature(
            id = "brass_pipes",
            names = listOf("brass pipes", "pipes", "copper tubes", "tubes", "steam pipes"),
            description = "An elaborate network of brass pipes and copper tubes runs along the " +
                    "walls and ceiling. Steam hisses softly through some pipes, while others " +
                    "carry glowing liquids of unknown purpose. The pipes are connected by " +
                    "intricate joints and valves that seem to regulate the flow.",
            keywords = listOf(
                "elaborate",
                "network",
                "ceiling",
                "hisses",
                "softly",
                "glowing",
                "liquids",
                "unknown",
                "purpose",
                "connected",
                "joints",
                "valves",
                "regulate",
                "flow"
            )
        ),
        RoomFeature(
            id = "central_device",
            names = listOf("central device", "clockwork device", "massive device", "mechanical device"),
            description = "A towering clockwork device dominates the center of the chamber. It " +
                    "features multiple levels of gears, dials with mysterious markings, brass " +
                    "levers, and rotating cylinders covered in symbols. The device appears to " +
                    "be some kind of complex calculating machine or puzzle mechanism.",
            keywords = listOf(
                "towering",
                "dominates",
                "center",
                "multiple",
                "levels",
                "dials",
                "mysterious",
                "markings",
                "levers",
                "rotating",
                "cylinders",
                "covered",
                "symbols",
                "complex",
                "calculating",
                "machine",
                "puzzle",
                "mechanism"
            )
        ),
        RoomFeature(
            id = "control_panel",
            names = listOf("control panel", "panel", "controls", "dials", "levers"),
            description = "A brass control panel is built into the side of the central device. " +
                    "It features numerous dials, switches, and levers, each labeled with " +
                    "cryptic symbols. Some controls glow softly, suggesting they are active " +
                    "and ready to be operated.",
            keywords = listOf(
                "built",
                "side",
                "numerous",
                "switches",
                "labeled",
                "cryptic",
                "glow",
                "active",
                "ready",
                "operated"
            )
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to AlchemistLaboratory::class,
    ),
    items = mutableListOf(
        ClockworkKey(),
        MechanicalDevice(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The chamber could have mechanical puzzles or timing challenges
        // Could also serve as a place to repair or enhance mechanical items
    }
}
