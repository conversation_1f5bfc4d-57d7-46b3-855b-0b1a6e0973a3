package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A book of prayers and spiritual guidance.
 */
class PrayerBook : Item(
    id = "prayer_book",
    name = "prayer book",
    description = "A leather-bound book filled with prayers, hymns, and spiritual wisdom. The " +
            "pages are made of fine parchment and the text is written in beautiful calligraphy " +
            "with illuminated letters. The book has been well-used, with many pages marked " +
            "by faithful readers. It radiates a sense of peace and devotion.",
    aliases = listOf("book", "prayers", "holy book", "sacred book"),
    weight = 2,
    isPickupable = true
), Useable {

    private val prayers = listOf(
        "Prayer of Protection" to "May the divine light shield me from harm and guide my steps on the righteous path.",
        "Prayer of Healing" to "Grant me the strength to heal both body and spirit, that I may serve others in their time of need.",
        "Prayer of Wisdom" to "Illuminate my mind with understanding and grant me the wisdom to make choices that bring light to the world.",
        "Prayer of Peace" to "Let peace fill my heart and flow through me to touch all those I encounter on my journey.",
        "Prayer of Courage" to "When darkness surrounds me, let me be a beacon of hope and courage for those who have lost their way."
    )

    private var currentPrayerIndex = 0

    override fun use(user: Player): String {
        val (title, text) = prayers[currentPrayerIndex]
        currentPrayerIndex = (currentPrayerIndex + 1) % prayers.size
        
        return "You open the prayer book and read the '$title':\n\n" +
                "\"$text\"\n\n" +
                "As you speak the words, you feel a sense of peace and spiritual strength " +
                "wash over you. The prayer brings comfort to your soul."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The book contains many prayers for different occasions - protection, healing, " +
                "wisdom, peace, and courage. You could 'use' the book to read a prayer and " +
                "gain spiritual comfort."
    }
}
