package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A torch that provides light in dark places.
 */
class Torch : Item(
    id = "torch",
    name = "wooden torch",
    description = "A sturdy wooden torch wrapped with oil-soaked cloth at one end. The wood " +
            "is smooth and well-crafted, designed to be held comfortably for extended periods. " +
            "The cloth wrapping is dark with oil and ready to be lit, promising hours of " +
            "bright, steady light.",
    aliases = listOf("torch", "wooden torch", "light"),
    weight = 2,
    isPickupable = true,
), Useable {

    private var isLit = false
    private var burnTimeRemaining = 100 // Represents burn time in arbitrary units

    /**
     * Light the torch.
     */
    fun light(): String {
        return if (!isLit) {
            isLit = true
            "You strike the torch against a rough surface, and it bursts into flame with a " +
                    "satisfying whoosh. The bright orange flame dances and flickers, casting " +
                    "warm light in all directions and driving back the shadows."
        } else {
            "The torch is already burning brightly."
        }
    }

    /**
     * Extinguish the torch.
     */
    fun extinguish(): String {
        return if (isLit) {
            isLit = false
            "You snuff out the torch flame, plunging the area back into darkness. Wisps of " +
                    "smoke curl up from the extinguished cloth, and the smell of burnt oil " +
                    "lingers in the air."
        } else {
            "The torch is not currently lit."
        }
    }

    /**
     * Check if the torch is currently lit.
     */
    fun isLit(): Boolean = isLit

    /**
     * Get the remaining burn time as a description.
     */
    fun getBurnTimeDescription(): String {
        return when {
            burnTimeRemaining > 75 -> "The torch burns brightly and should last for hours."
            burnTimeRemaining > 50 -> "The torch burns steadily with plenty of fuel remaining."
            burnTimeRemaining > 25 -> "The torch flame is starting to dim slightly."
            burnTimeRemaining > 10 -> "The torch is burning low and will need replacement soon."
            burnTimeRemaining > 0 -> "The torch flame flickers weakly and may go out at any moment."
            else -> "The torch has burned out completely."
        }
    }

    /**
     * Reduce burn time (called periodically when lit).
     */
    fun burn() {
        if (isLit && burnTimeRemaining > 0) {
            burnTimeRemaining--
            if (burnTimeRemaining <= 0) {
                isLit = false
            }
        }
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            if (isLit) {
                appendLine("The torch burns with a bright, warm flame.")
                appendLine(getBurnTimeDescription())
            } else {
                appendLine("The torch is not lit. You could try to 'light' it.")
                if (burnTimeRemaining > 0) {
                    appendLine("The oil-soaked cloth is ready to burn.")
                } else {
                    appendLine("The torch has burned out and cannot be relit.")
                }
            }
        }
    }

    override fun getShortDescription(): String {
        return if (isLit) {
            "burning torch"
        } else {
            name
        }
    }

    override fun use(user: Player): String {
        return if (!isLit()) {
            light()
        } else {
            "The torch is already lit."
        }
    }
}
