package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.doors.GardenDoor
import com.terheyden.mud.mudlib.items.GlowingCrystal
import com.terheyden.mud.mudlib.items.HealingPotion
import org.springframework.stereotype.Component

/**
 * A hidden garden behind a locked door, filled with magical plants and treasures.
 */
@Component
class SecretGarden : Room(
    id = "secret_garden",
    name = "Secret Garden",
    description = "You stand in a breathtaking secret garden hidden away from the world. " +
            "Luminescent flowers bloom in impossible colors, their petals shimmering with magical energy. " +
            "Ancient stone paths wind between beds of rare herbs and mystical plants. " +
            "A small fountain in the center bubbles with crystal-clear water that seems to glow from within. " +
            "The air is thick with the scent of exotic blooms and magical energy.",
    features = mutableListOf(
        RoomFeature(
            id = "luminescent_flowers",
            names = listOf("luminescent flowers", "flowers", "magical flowers", "glowing flowers"),
            description = "These extraordinary flowers seem to glow with their own inner light. " +
                    "Their petals shift through a spectrum of colors - deep purples, brilliant blues, " +
                    "and shimmering golds. As you watch, tiny motes of light drift from their centers, " +
                    "creating a magical aurora in the air around them.",
            keywords = listOf("glow", "inner", "light", "petals", "spectrum", "colors", "motes", "aurora")
        ),
        RoomFeature(
            id = "ancient_paths",
            names = listOf("ancient paths", "stone paths", "paths", "stones"),
            description = "The paths are made of smooth, weathered stones that have been worn by countless " +
                    "years of use. Strange runes are carved into some of the stones, and they seem to " +
                    "pulse faintly with magical energy. The paths form intricate patterns that guide " +
                    "visitors through the garden in a way that feels both natural and mystical.",
            keywords = listOf("smooth", "weathered", "runes", "carved", "pulse", "patterns", "mystical")
        ),
        RoomFeature(
            id = "magical_fountain",
            names = listOf("fountain", "magical fountain", "crystal fountain", "glowing fountain"),
            description = "The fountain is carved from a single piece of white crystal that seems to " +
                    "emit its own soft light. The water that flows from it is perfectly clear and " +
                    "sparkles with tiny points of light. As you listen, you can hear a faint, " +
                    "melodic humming coming from the water itself. The fountain radiates a sense " +
                    "of peace and healing.",
            keywords = listOf(
                "carved",
                "crystal",
                "white",
                "clear",
                "sparkles",
                "melodic",
                "humming",
                "peace",
                "healing",
            ),
        ),
        RoomFeature(
            id = "rare_herbs",
            names = listOf("rare herbs", "herbs", "mystical plants", "plants", "magical herbs"),
            description = "These are no ordinary garden plants. Each herb seems to possess its own " +
                    "unique magical properties. Some have leaves that change color as you watch, " +
                    "others emit soft chimes when the wind touches them, and a few seem to bend " +
                    "toward you as if aware of your presence. The knowledge of their uses has been " +
                    "lost to time, but their power is unmistakable.",
            keywords = listOf("ordinary", "properties", "change", "chimes", "wind", "bend", "aware", "power")
        ),
    ),
    exits = mutableMapOf(
        Direction.EAST to ForestClearing::class,
        Direction.WEST to EnchantedGrove::class,
    ),
    doors = mutableListOf(GardenDoor(Direction.EAST)),
    items = mutableListOf(
        HealingPotion(),
        GlowingCrystal(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The garden could have special effects when players enter
        // - Healing effects from the magical atmosphere
        // - Temporary magical buffs
        // - Special events or encounters
    }
}
