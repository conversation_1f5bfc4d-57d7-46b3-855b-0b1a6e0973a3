package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item

/**
 * A mysterious key that can open many locks.
 */
class SkeletonKey : Item(
    id = "skeleton_key",
    name = "skeleton key",
    description = "An ornate brass key with an intricate design that seems to shift and change " +
            "when you're not looking directly at it. The key feels warm to the touch and " +
            "hums with a faint magical energy. Legend says such keys can open any lock, " +
            "but they come with a price.",
    aliases = listOf("key", "brass key", "magical key", "ornate key"),
    weight = 1,
    isPickupable = true,
) {

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This key bears the mark of powerful enchantment and could unlock many secrets.")
            appendLine("However, using such magic always comes with consequences.")
        }
    }
}
