package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.MagicalPotion

/**
 * A mysterious potion seller who deals in magical brews and elixirs.
 */
class PotionSeller : NPC(
    id = "potion_seller",
    name = "potion seller",
    description = "A mysterious figure in a dark hooded cloak who tends a small stall filled " +
            "with bubbling vials and glowing potions. Their face is hidden in shadow, but " +
            "their hands are steady and sure as they work with their magical brews. The " +
            "air around them shimmers with arcane energy, and strange scents waft from " +
            "their collection of bottles and vials.",
    inventory = mutableListOf(
        HealingPotion(), HealingPotion(), HealingPotion(),
        MagicalPotion(), MagicalPotion(),
        GoldCoins()
    ),
    maxHealthPoints = 80,
    currentHealthPoints = 80,
    level = 4,
    baseAttackPower = 6,
    baseDefense = 12,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    override fun getGreeting(player: Player): String {
        return "The hooded figure looks up from their bubbling cauldron. 'Greetings, seeker. " +
                "I deal in potions and elixirs of all kinds. Healing draughts, magical " +
                "enhancers, protective brews - what does your journey require?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("potion") || lowerMessage.contains("healing") -> {
                "The potion seller holds up a glowing red vial. 'Healing potions are my " +
                        "specialty. These will mend wounds and restore vitality quickly. " +
                        "Essential for any adventurer. Twenty-five gold pieces each.'"
            }

            lowerMessage.contains("magic") || lowerMessage.contains("enhancement") -> {
                "The seller's eyes glint beneath their hood. 'Ah, magical enhancement potions. " +
                        "These can temporarily boost your abilities or provide special effects. " +
                        "Very useful, but also very expensive. Fifty gold pieces each.'"
            }

            else -> {
                "The potion seller nods mysteriously. 'Indeed... most interesting.'"
            }
        }
    }
}
