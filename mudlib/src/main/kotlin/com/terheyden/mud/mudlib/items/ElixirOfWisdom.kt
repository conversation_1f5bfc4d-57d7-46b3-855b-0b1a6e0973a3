package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A powerful elixir that enhances wisdom and magical understanding.
 */
class ElixirOfWisdom : Item(
    id = "elixir_of_wisdom",
    name = "Elixir of Wisdom",
    description = "A crystal vial containing a golden liquid that seems to glow with inner light. " +
            "The elixir swirls slowly within the vial, creating mesmerizing patterns that seem " +
            "to contain glimpses of ancient knowledge. The liquid gives off a faint, pleasant " +
            "aroma that reminds you of libraries, starlight, and the moment of understanding.",
    aliases = listOf("elixir", "wisdom elixir", "golden elixir", "vial", "potion"),
    weight = 1,
    isPickupable = true
), Useable {

    private var hasBeenUsed = false

    override fun use(user: Player): String {
        return if (!hasBeenUsed) {
            hasBeenUsed = true
            "You drink the Elixir of Wisdom in one smooth gulp. The golden liquid tastes like " +
                    "honey and starlight, and as it flows down your throat, you feel your mind " +
                    "expanding. Ancient knowledge flows through your consciousness, and for a " +
                    "moment, you understand the deeper mysteries of the world. The empty vial " +
                    "crumbles to crystal dust in your hands, its purpose fulfilled."
        } else {
            "The vial is empty - its wisdom has already been consumed."
        }
    }

    override fun getExamineDescription(): String {
        return if (!hasBeenUsed) {
            super.getExamineDescription() + "\n\n" +
                    "The elixir seems to respond to your gaze, swirling faster and glowing brighter. " +
                    "You sense that drinking this would grant you great wisdom, but only once - " +
                    "such powerful magic cannot be repeated."
        } else {
            "An empty crystal vial that once contained the Elixir of Wisdom. Though empty, " +
                    "it still carries a faint glow and the memory of the knowledge it once held."
        }
    }
}
