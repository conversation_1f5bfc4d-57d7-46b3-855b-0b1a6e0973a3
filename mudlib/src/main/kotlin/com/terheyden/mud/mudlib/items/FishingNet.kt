package com.terheyden.mud.mudlib.items

import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.Useable
import com.terheyden.mud.corelib.living.Player

/**
 * A fishing net used for catching fish in the river.
 */
class FishingNet : Item(
    id = "fishing_net",
    name = "fishing net",
    description = "A well-made fishing net woven from strong cord. The net is designed with " +
            "a fine mesh that can catch fish while allowing water to flow through freely. " +
            "The edges are reinforced with thicker rope, and the net shows signs of regular " +
            "use and careful maintenance. This is clearly the tool of an experienced fisherman.",
    aliases = listOf("net", "fishing", "mesh", "cord net"),
    weight = 5,
    isPickupable = true
), Useable {

    override fun use(user: Player): String {
        return "You examine the fishing net carefully, testing the strength of the cord and " +
                "checking for any tears or weak spots. The net is well-maintained and would " +
                "be perfect for catching fish in the river. The mesh is the right size to " +
                "catch good-sized fish while letting the small ones escape to grow larger."
    }

    override fun getExamineDescription(): String {
        return super.getExamineDescription() + "\n\n" +
                "The net appears to be in excellent condition, with tight, even weaving " +
                "and no visible tears. It would be very effective for fishing in the river."
    }
}
