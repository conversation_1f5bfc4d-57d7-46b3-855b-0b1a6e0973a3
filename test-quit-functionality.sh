#!/bin/bash

echo "=== Testing Quit Functionality ==="
echo ""
echo "This test will verify that the application properly shuts down when the player quits."
echo "The application should:"
echo "1. Start normally with tick system running"
echo "2. Process the 'quit' command"
echo "3. Gracefully shut down all services"
echo "4. Exit completely without hanging"
echo ""

# Create input commands for the game
cat > quit_test_input.txt << 'EOF'
look
tick
quit
EOF

echo "Starting game and testing quit functionality..."
echo ""

# Run the game with input and timeout as a safety net
timeout 15s ./gradlew bootRun < quit_test_input.txt

exit_code=$?

echo ""
if [ $exit_code -eq 0 ]; then
    echo "✅ SUCCESS: Application shut down gracefully!"
elif [ $exit_code -eq 124 ]; then
    echo "❌ FAILURE: Application timed out (still hanging after 15 seconds)"
else
    echo "⚠️  WARNING: Application exited with code $exit_code"
fi

# Clean up
rm -f quit_test_input.txt

echo ""
echo "Test completed."
