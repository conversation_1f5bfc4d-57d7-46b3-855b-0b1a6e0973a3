# Spring MUD

A text-based Multi-User Dungeon (MUD) game built with Spring Boot and Kotlin,
inspired by classic interactive fiction games like Zork and LPMud.

## Features

### Core Gameplay
- **Console-based gameplay** with text descriptions and command input
- **Fantasy world** with interconnected rooms to explore
- **Movement system** supporting cardinal directions (north, south, east, west, up, down)
- **Items and inventory system** with weight limits and item interactions
- **Item aliases** - refer to items naturally (e.g., "potion" instead of "healing potion")
- **Interactive room features** - examine environmental details mentioned in room descriptions
- **Command system** with aliases and help functionality

### Advanced Systems
- **Background tick system** - Autonomous NPC behavior and time-based events
- **Living NPCs** - Characters that speak and act independently over time
- **Turn-based combat** - Fight wolves and other creatures with experience/leveling
- **Health regeneration** - Automatic healing over time via the tick system
- **Dynamic logging** - Real-time debug control with `set debug on/off`
- **Configurable settings** - Adjust colors, logging, and system parameters in-game

### Technical Features
- **Spring Boot architecture** with dependency injection and clean separation of concerns
- **Modern Spring patterns** - InitializingBean, dependency ordering, lifecycle management
- **Comprehensive testing** - Unit and integration tests for all major systems
- **Performance optimized** - Configurable tick intervals and efficient processing

## How to Run

### Option 1: Using the run script (Recommended)

```bash
./run-game.sh
```

### Option 2: Using Gradle directly

```bash
./gradlew bootRun
```

### Option 3: Running the JAR file

```bash
./gradlew bootJar
java -jar build/libs/spring-mud-0.0.1-SNAPSHOT.jar
```

## How to Play

1. Start the game using one of the methods above
2. Enter your character name when prompted
3. Read the room description and available exits
4. Use movement commands to explore the world
5. Type `help` to see all available commands
6. Type `quit` to exit the game

## Architecture

The game follows clean architecture principles, and is organized into three modules:

1. **corelib** - Core interfaces and base classes
2. **engine** - Spring Boot application, game engine, and console runner
3. **mudlib** - Default game world content (rooms, items, etc.)

### corelib

Contains the core interfaces and base classes for the game, including:

- `GameWorld` - Manages the collection of rooms and world state
- `Player` - Represents the player character with inventory
- `Room` - Represents game locations with descriptions, connections, items, and interactive features
- `Item` - Represents items with properties like weight and description
- `Command` - Interface for all game commands
- `Direction` - Enum for movement directions with aliases

`corelib` is inherited as a dependency by `engine` and `mudlib`.

### engine

Contains the Spring Boot application, game engine, and commands. It depends on `corelib`.

### mudlib

Contains the default game world content, including rooms, items, and interactive features. It depends on `corelib`.

## Testing

Run the test suite:

```bash
./gradlew test
```

## Technology Stack

- **Kotlin** - Primary programming language
- **Spring Boot 3.5.3** - Application framework
- **Gradle** - Build tool
- **JUnit 5** - Testing framework

# Room Map

```
                                         Dungeon - Abandoned       
                                         Chamber     Mine          
                                            |                      
                              Mirror    Underground                
                               Maze       Tunnel                   
                                |           |                      
                Clockwork - Alchemist  -  Tower   /  Tower         
                 Chamber    Laboratory   Basement   Entrance       
                                                       |           
                                                       *           
                                                                   
                                 Elemental                         
                                  Shrine                           
                                    |                              
                        Ancient - Runic                            
                        Library   Circle                           
                           |                                       
                         Tower                                     
                        Chamber                                    
                           /                                       
                         Tower   -*                                
                        Entrance                                   
                           |                                       
Enchanted - Secret - Forest Clearing - Crystal                     
  Grove     Garden      (START)         Cave                       
                           |                                       
                           |                                       
                         Forest                                    
                          Path     Village    Village   -  Village 
                           |      Blacksmith  Outskirts   Guardpost
                           |          |          |                 
               Village - Village - Village - Village               
                Temple   Square     Market    Docks                
                  |         |         |                            
               Village   Village   Village                         
                Library    Inn      Bakery                         
```
