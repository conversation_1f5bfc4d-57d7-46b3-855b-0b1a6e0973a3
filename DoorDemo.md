# Door System Implementation - Demo

## Overview
I've successfully implemented a comprehensive door system for your Spring Boot MUD game! Here's what was added:

## New Features

### 1. **Core Door Infrastructure**
- `Door` class that extends `RoomFeature` with lock/unlock, open/close functionality
- `Lock` interface with `KeyLock` and `SimpleLock` implementations
- Doors can be locked/unlocked, opened/closed, and block movement when closed or locked

### 2. **New Commands**
- `open <door>` - Opens a door (if unlocked)
- `close <door>` - Closes a door
- `unlock <door> with <key>` - Unlocks a door with a key
- `lock <door> with <key>` - Locks a door with a key

### 3. **Enhanced Game World**
- **New Room**: Secret Garden - A magical hidden garden behind a locked door
- **Enhanced Forest Clearing**: Now has a locked door leading west to the Secret Garden
- **Updated Movement**: Movement is blocked by closed/locked doors with helpful messages

### 4. **Integration with Existing Items**
- The existing `RustyKey` now unlocks the new garden door
- Key already supports the "ancient_door" lock ID used by the garden door

## How to Experience the Door System

### Step 1: Start in Forest Clearing
```
=== Forest Clearing ===
You stand in a peaceful clearing surrounded by tall oak trees...
Exits: north, east, west
```

### Step 2: Try to Go West (Blocked!)
```
> west
The garden door is locked. You need to unlock it first.
```

### Step 3: Examine the Door
```
> examine door
Almost hidden among the thick oak trees to the west, you notice an old wooden door 
set into what appears to be a natural stone archway covered in vines. The door is made 
of dark, weathered wood and is secured with an ancient iron lock. Strange symbols are 
carved around the doorframe, and the whole entrance has an air of mystery and magic. 
The door is currently closed and locked.
```

### Step 4: Get the Rusty Key
```
> north
> take rusty key
You take the rusty key.
> south
```

### Step 5: Unlock the Door
```
> unlock door with rusty key
You unlock the garden door with the rusty key.
```

### Step 6: Open the Door
```
> open door
You open the garden door.
```

### Step 7: Enter the Secret Garden
```
> west
You go west.

=== Secret Garden ===
You stand in a breathtaking secret garden hidden away from the world. 
Luminescent flowers bloom in impossible colors, their petals shimmering with magical energy...

Items: healing potion, glowing crystal
Exits: west
```

## Technical Implementation Details

### Door States
- **Locked + Closed**: Blocks movement, requires key to unlock
- **Unlocked + Closed**: Blocks movement, can be opened
- **Unlocked + Open**: Allows free passage

### Key System
- Uses existing `RustyKey.canUnlock()` method
- Supports multiple lock types through reflection
- Extensible for future key/lock combinations

### Room Integration
- Doors are `RoomFeature` objects that can be examined
- Movement system checks door states before allowing passage
- Helpful error messages guide players

## Files Modified/Created

### New Core Files:
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/room/Door.kt`
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/room/Lock.kt`
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/OpenCommand.kt`
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/CloseCommand.kt`
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/UnlockCommand.kt`
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/LockCommand.kt`

### New Game Content:
- `mudlib/src/main/kotlin/com/terheyden/mud/mudlib/rooms/SecretGarden.kt`

### Enhanced Files:
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/room/Room.kt` (door management)
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/room/RoomFeature.kt` (made extensible)
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/MoveCommand.kt` (door checking)
- `corelib/src/main/kotlin/com/terheyden/mud/corelib/command/ExamineCommand.kt` (door descriptions)
- `mudlib/src/main/kotlin/com/terheyden/mud/mudlib/rooms/ForestClearing.kt` (added door)
- `mudlib/src/main/kotlin/com/terheyden/mud/mudlib/items/RustyKey.kt` (updated description)

## Future Enhancements

The door system is designed to be extensible:

1. **Multiple Key Types**: Easy to add new key items that work with different locks
2. **Complex Locks**: Could add combination locks, magical locks, etc.
3. **Door Types**: Could add different door materials, magical doors, etc.
4. **Automatic Doors**: Could add doors that open/close based on conditions
5. **Trapped Doors**: Could add doors with traps or special effects

The implementation follows the existing game patterns and integrates seamlessly with the current command system and room structure!
