<!-- https://logging.apache.org/log4j/2.x/manual/configuration.html -->
<!-- Auto-reload this config every 300 secs (5 mins) -->
<Configuration status="INFO" monitorInterval="300">
    <!-- Appenders configure how + where log messages should go -->
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <!-- https://logging.apache.org/log4j/2.x/manual/layouts.html#PatternLayout -->
            <!-- Note that an exception specifier (%xEx) is appended if not specified -->
            <PatternLayout pattern="%date{HH:mm:ss.SSS} [%thread] [%-5level] %-30.30logger{1.2.*} - %msg%n"/>
        </Console>
    </Appenders>

    <!-- Loggers configure what (packages) should be logged, and which Appender to use -->
    <Loggers>
        <!-- log just this package at TRACE -->
        <!-- additivity = false means stop adding any more loggers
             (don't also add the root logger; avoids duplicates) -->
        <Logger name="com.terheyden" level="TRACE" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- in tests, set default to DEBUG -->
        <Root level="DEBUG">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>
