package com.terheyden.mud.corelib.event

import com.terheyden.mud.corelib.GameService.events
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class EventServiceTest {

    @Test
    fun `should subscribe and publish events`() {
        var counter = 0

        // Subscribe to our test event:
        val sub = events.subscribe(TestEvent::class) { event ->
            println("Received event: ${event.message}")
            counter++
        }

        // Publish an event:
        events.publish(TestEvent("Hello, world!"))

        // Unsubscribe:
        sub.unsubscribe()

        // Publish again, should not be received:
        events.publish(TestEvent("Hello again, world!"))

        // Should only have received one event:
        assertThat(counter).isEqualTo(1)
    }
}
