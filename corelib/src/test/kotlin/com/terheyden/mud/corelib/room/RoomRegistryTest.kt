package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.testlib.NorthRoom
import com.terheyden.mud.corelib.testlib.SouthRoom
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RoomRegistryTest {

    @Test
    fun test() {
        val southRoom1 = SouthRoom()
        val registry = RoomRegistry(listOf(southRoom1, NorthRoom()), southRoom1)
        val southRoom2 = registry.getRoom(SouthRoom::class)
        val northRoom = registry.getRoom(NorthRoom::class)
        val exit = registry.resolveExit(southRoom2, Direction.NORTH)

        assertThat(exit).isEqualTo(northRoom)
    }
}
