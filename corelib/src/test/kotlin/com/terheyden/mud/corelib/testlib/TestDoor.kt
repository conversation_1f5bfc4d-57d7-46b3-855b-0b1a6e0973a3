package com.terheyden.mud.corelib.testlib

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Door

class TestDoor(
    direction: Direction,
) : Door(
    id = "test_door",
    name = "a test door",
    aliases = listOf("door", "test door"),
    description = "A test door.",
    direction = direction,
    closed = true,
    locked = true,
    requiredKeyId = "test_key",
)
