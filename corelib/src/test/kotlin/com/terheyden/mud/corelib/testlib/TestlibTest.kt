package com.terheyden.mud.corelib.testlib

import com.terheyden.mud.corelib.GameInitService
import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.GameWorld
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import org.junit.jupiter.api.BeforeEach

open class TestlibTest {

    @BeforeEach
    fun setUp() {
        val startRoom = SouthRoom()
        GameService.world = GameWorld(startRoom)
        GameService.player = Player()
        GameService.rooms = RoomRegistry(listOf(startRoom, NorthRoom()), startRoom)

        val gameInitService = GameInitService(GameService.rooms)
        gameInitService.afterPropertiesSet()
    }
}
