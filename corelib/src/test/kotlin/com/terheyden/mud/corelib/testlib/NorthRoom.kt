package com.terheyden.mud.corelib.testlib

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import org.springframework.stereotype.Component

/**
 * The peaceful forest clearing where players begin their adventure.
 */
@Component
class NorthRoom : Room(
    id = "north_room",
    name = "North Room",
    features = mutableListOf(
        RoomFeature(
            id = "circular_room",
            names = listOf("circular room", "room"),
            description = "The room is circular in shape.",
            keywords = listOf("circular", "shape"),
        ),
    ),
    description = "You stand in the north room. It is circular in shape.",
    exits = mutableMapOf(
        Direction.SOUTH to SouthRoom::class,
    ),
    doors = mutableListOf(TestDoor(Direction.SOUTH)),
)
