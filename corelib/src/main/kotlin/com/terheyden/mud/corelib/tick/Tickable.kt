package com.terheyden.mud.corelib.tick

/**
 * Interface for objects that can receive periodic tick updates.
 * This is the core interface for the MUD's heartbeat system.
 */
interface Tickable {

    /**
     * Whether this object should receive tick updates.
     * Objects can disable ticking to save performance when inactive.
     */
    fun isTickEnabled(): Boolean = true

    /**
     * How often this object should be ticked (in ticks).
     * Default is every tick (1). Higher values mean less frequent updates.
     * For example, return 10 to be ticked every 10th tick.
     */
    fun getTickInterval(): Int = 1

    /**
     * Called after each [getTickInterval()] ticks.
     *
     * @param tickCount The current tick number since game start
     */
    fun onTick(tickCount: Long)
}
