package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.GameService.tickService
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.corelib.tick.Tickable
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Service

/**
 * Initializes the player, rooms, and their contents once they are fully loaded.
 * Sets up and verifies state before the game starts.
 */
@Service
class GameInitService(
    private val rooms: RoomRegistry,
) : InitializingBean {

    private val logger = KotlinLogging.logger {}
    private val initMap = StringBuilder().apply {
        appendLine("Spring MUD World Map:")
    }

    override fun afterPropertiesSet() {
        logger.info { "Initializing game world..." }
        rooms.forEach { initializeRoom(it) }
        logger.info { "GameInitService completed initialization!" }
        logger.debug { initMap.toString() }
    }

    private fun initializeRoom(room: Room) {
        // Init the room itself:
        initMap.appendLine("  ${room.id}")
        room.onInit()

        // Init the room doors:
        room.doors.values.forEach { initializeItem(room, it, 1) }

        // Init the room items:
        room.items.forEach { initializeItem(room, it, 1) }
    }

    private fun initializeItem(itemEnv: Container, item: Item, depth: Int) {
        initMap.appendLine("  ${"  ".repeat(depth)}${item.id}")

        // Make sure the env is set.
        item.currentEnv = itemEnv

        // Init the item:
        if (item is Initializable) item.onInit()

        // Register the item for ticks:
        if (item is Tickable) tickService.register(item)

        // Recurse if the item is a container:
        if (item is Container) {
            item.items.forEach { initializeItem(item, it, depth + 1) }
        }
    }
}
