package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

/**
 * For managing room instances in the game world.
 */
@Component
class RoomRegistry(
    rooms: List<Room>,
    val startingRoom: StartingRoom,
) : Iterable<Room> by rooms, InitializingBean {

    val theVoid = TheVoid.Instance
    private val logger = KotlinLogging.logger {}

    private val roomsByClass: Map<KClass<out Room>, Room> = rooms
        .toMutableList()
        .also { it.add(theVoid) }
        .associateBy { it::class }

    /**
     * Get a room instance by its class.
     */
    fun getRoom(roomClass: KClass<out Room>) = roomsByClass[roomClass]
        ?: throw IllegalArgumentException("Room class not found: ${roomClass.simpleName}")

    /**
     * Resolve a room exit - handles both string IDs and room classes.
     */
    fun resolveExit(fromRoom: Room, direction: Direction): Room? =
        fromRoom.findExit(direction)?.let { exitRoomClass ->
            getRoom(exitRoomClass)
        }

    /** As soon as we're loaded, add ourselves to the GameService shim. */
    override fun afterPropertiesSet() {
        logger.info { "RoomRegistry initialized!" }
        GameService.rooms = this
    }
}
