package com.terheyden.mud.corelib.event

import com.terheyden.mud.corelib.GameObject
import kotlin.reflect.KClass

class ObjectService {

    private var uid = 0L
    private val gameObjects = mutableMapOf<KClass<out GameObject>, MutableList<GameObject>>()

    fun nextUid() = uid++

    fun registerGameObject(gameObject: GameObject) {
        gameObjects.getOrPut(gameObject::class) { mutableListOf() }.add(gameObject)
    }

    @Suppress("UNCHECKED_CAST")
    fun <T : GameObject> getGameObjects(gameObjectClass: KClass<out GameObject>): List<T> {
        return gameObjects[gameObjectClass] as List<T>? ?: emptyList()
    }
}
