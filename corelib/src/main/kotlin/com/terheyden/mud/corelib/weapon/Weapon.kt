package com.terheyden.mud.corelib.weapon

import com.terheyden.mud.corelib.Item

/**
 * Represents a weapon that can be equipped and used in combat.
 */
open class Weapon(
    id: String,
    name: String,
    description: String,
    aliases: List<String> = emptyList(),
    weight: Int = 1,
    val damage: Int = 5,
    val weaponType: WeaponType = WeaponType.MELEE,
    val durability: Int = 100,
    var currentDurability: Int = durability,
) : Item(
    id = id,
    name = name,
    description = description,
    aliases = aliases,
    weight = weight,
    isPickupable = true,
) {

    /**
     * Check if the weapon is broken.
     */
    fun isBroken(): Boolean = currentDurability <= 0

    /**
     * Check if the weapon is in good condition.
     */
    fun isInGoodCondition(): Boolean = currentDurability >= durability * 0.7

    /**
     * Get the condition description of the weapon.
     */
    fun getConditionDescription(): String {
        val percentage = (currentDurability.toDouble() / durability * 100).toInt()
        return when {
            percentage >= 90 -> "excellent condition"
            percentage >= 70 -> "good condition"
            percentage >= 50 -> "fair condition"
            percentage >= 30 -> "poor condition"
            percentage >= 10 -> "terrible condition"
            else -> "broken"
        }
    }

    /**
     * Reduce durability from use.
     */
    fun reduceDurability(amount: Int = 1) {
        currentDurability = maxOf(0, currentDurability - amount)
    }

    /**
     * Repair the weapon.
     */
    fun repair(amount: Int) {
        currentDurability = minOf(durability, currentDurability + amount)
    }

    /**
     * Get effective damage based on condition.
     */
    fun getEffectiveDamage(): Int {
        if (isBroken()) return 0
        val conditionMultiplier = currentDurability.toDouble() / durability
        return (damage * conditionMultiplier).toInt().coerceAtLeast(1)
    }

    /**
     * Get a detailed examine description including condition and stats.
     */
    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("Weapon Type: ${weaponType.displayName}")
            appendLine("Damage: ${getEffectiveDamage()}")
            appendLine("Condition: ${getConditionDescription()} ($currentDurability/$durability)")
            if (isBroken()) {
                appendLine("This weapon is broken and cannot be used effectively!")
            }
        }
    }

    override fun toString() =
        "Weapon(" +
                "damage=$damage, " +
                "weaponType=$weaponType, " +
                "durability=$currentDurability/$durability) " +
                super.toString()
}

/**
 * Types of weapons available in the game.
 */
enum class WeaponType(val displayName: String) {
    MELEE("Melee Weapon"),
    RANGED("Ranged Weapon"),
    MAGICAL("Magical Weapon"),
    IMPROVISED("Improvised Weapon")
}
