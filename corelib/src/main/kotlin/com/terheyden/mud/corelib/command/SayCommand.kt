package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to talk to NPCs or speak in general.
 */
@Component
class SayCommand : Command {
    override val name = "say"
    override val aliases = listOf("talk", "speak", "tell")
    override val description = "Say something or talk to an NPC"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Say what? Specify a message to say.")
        }

        val player = GameService.player
        val currentRoom = player.getCurrentRoom()
        val message = args.joinToString(" ")

        // Check if there are NPCs in the room to respond
        val npcsInRoom = currentRoom.contentsNPCs

        if (npcsInRoom.isEmpty()) {
            return CommandResult("You say: \"$message\"\nThere is no one here to respond.")
        }

        val result = StringBuilder()
        result.appendLine("You say: \"$message\"")
        result.appendLine()

        // Have each NPC respond to the message
        npcsInRoom.forEach { npc ->
            if (npc.isAlive()) {
                val response = npc.handleDialogue(player, message)
                result.appendLine("${npc.name} says: \"$response\"")
            }
        }

        return CommandResult(result.toString())
    }
}
