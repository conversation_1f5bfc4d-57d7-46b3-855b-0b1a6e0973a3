package com.terheyden.mud.corelib.living

import com.terheyden.mud.corelib.GameService
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component

/**
 * Represents the player character in the game.
 */
@Component
class Player(
    id: String = "player",
    name: String = "Adventurer",
    description: String = "A brave adventurer.",
    var colorEnabled: Boolean = true,
) : Living(
    id = id,
    name = name,
    description = description,
), InitializingBean {

    private val logger = KotlinLogging.logger {}

    /** As soon as we're loaded, add ourselves to the GameService shim. */
    override fun afterPropertiesSet() {
        logger.info { "Player initialized!" }
        GameService.player = this
    }
}
