package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.room.Door
import org.springframework.stereotype.Component

/**
 * Command to close doors.
 */
@Component
class CloseCommand : Command {
    override val name = "close"
    override val aliases = listOf("shut")
    override val description = "Close a door"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Close what? Specify a door to close.")
        }

        val player = GameService.player
        val currentRoom = player.getCurrentRoom()
        val doorName = args.joinToString(" ")

        // Find the door in room features
        val feature = currentRoom.findFeature(doorName)
        if (feature !is Door) {
            return CommandResult("There is no '$doorName' here that can be closed.")
        }

        // Attempt to close the door
        return CommandResult(feature.close())
    }
}
