package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to look around the current room.
 */
@Component
class LookCommand : Command {
    override val name = "look"
    override val aliases = listOf("l")
    override val description = "Look around the current room"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        val currentRoom = player.getCurrentRoom()

        val result = StringBuilder()
        result.append(currentRoom.getFullDescription())

        // Show any recent room messages (NPC speech, actions, etc.)
        val roomMessages = currentRoom.getAndClearRoomMessages()
        if (roomMessages.isNotEmpty()) {
            result.appendLine()
            result.appendLine("*Recent activity:*")
            roomMessages.forEach { message ->
                result.appendLine("  $message")
            }
        }

        return CommandResult(result.toString())
    }
}
