package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.command.Command
import com.terheyden.mud.corelib.event.EventService
import com.terheyden.mud.corelib.event.ObjectService
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.corelib.tick.TickService

/**
 * Static access to the game state and world.
 * A shim to Spring objects for non-Spring lib objects.
 */
object GameService {

    val objects: ObjectService = ObjectService()
    val events: EventService = EventService()
    val tickService: TickService = TickService()
    lateinit var world: GameWorld
    lateinit var player: Player
    lateinit var rooms: RoomRegistry

    /**
     * Optional command to process raw user input before normal command processing.
     * Set this back to null to resume normal processing.
     */
    var inputHookCommand: Command? = null
}
