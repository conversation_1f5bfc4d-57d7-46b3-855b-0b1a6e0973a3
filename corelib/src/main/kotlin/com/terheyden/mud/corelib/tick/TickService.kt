package com.terheyden.mud.corelib.tick

import com.terheyden.mud.corelib.GameService.events
import io.github.oshai.kotlinlogging.KotlinLogging

/**
 * Called by the scheduler every tick (1 second).
 * Handles ticks in the game world.
 */
class TickService {

    private val logger = KotlinLogging.logger {}
    private val tickables = mutableListOf<Tickable>()

    init {
        events.subscribe(TickEvent::class) { tick(it) }
    }

    /**
     * Register a tickable object.
     */
    fun register(tickable: Tickable) {
        tickables.add(tickable)
    }

    /**
     * Unregister a tickable object.
     */
    fun unregister(tickable: Tickable) {
        tickables.remove(tickable)
    }

    /**
     * Get tickables that should be processed on this tick.
     */
    private fun getActiveTickables(currentTick: Long): List<Tickable> {
        return tickables.filter { tickable ->
            tickable.isTickEnabled() &&
                    (currentTick % tickable.getTickInterval() == 0L)
        }
    }

    /**
     * Called by the scheduler every tick.
     * Processes all active tickables.
     */
    fun tick(tickEvent: TickEvent) {
        val activeTickables = getActiveTickables(tickEvent.tickCount)
        activeTickables.forEach { it.onTick(tickEvent.tickCount) }
    }

    /**
     * Clear all registered tickables.
     */
    fun clear() {
        val count = tickables.size
        tickables.clear()
        logger.info { "Cleared $count tickables from registry" }
    }
}
