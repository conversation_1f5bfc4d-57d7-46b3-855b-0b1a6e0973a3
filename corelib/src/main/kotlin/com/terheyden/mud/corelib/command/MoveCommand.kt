package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.room.Room
import org.springframework.stereotype.Component

/**
 * Command to move the player in a direction.
 */
@Component
class MoveCommand : Command {
    override val name = "move"
    override val aliases = listOf("go", "walk")
    override val description = "Move in a direction (north, south, east, west, etc.)"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        val currentRoom = player.getCurrentRoom()

        // Determine the direction to move
        val direction = if (args.isNotEmpty()) {
            Direction.fromString(args[0])
        } else {
            null
        }

        if (direction == null) {
            return CommandResult("It looks like you can move: ${getMovableDirections(currentRoom)}")
        }

        return CommandResult(GameCommands.move(player, direction))
    }

    private fun getMovableDirections(room: Room): String {
        val availableExits = room.getAvailableExits()
        if (availableExits.isEmpty()) return "nowhere!"

        return availableExits.joinToString(", ") { it.name.lowercase() }
    }
}
