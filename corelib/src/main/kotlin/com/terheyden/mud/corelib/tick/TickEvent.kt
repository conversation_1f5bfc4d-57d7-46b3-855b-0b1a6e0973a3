package com.terheyden.mud.corelib.tick

import com.terheyden.mud.corelib.event.GameEvent

/**
 * Event published on each game tick.
 */
data class TickEvent(
    val tickCount: Long,
    val deltaTime: Long, // Time since last tick in milliseconds
) : GameEvent

/**
 * Event published when the tick system starts.
 */
data class TickSystemStartedEvent(
    val startTime: Long = System.currentTimeMillis(),
) : GameEvent

/**
 * Event published when the tick system stops.
 */
data class TickSystemStoppedEvent(
    val stopTime: Long = System.currentTimeMillis(),
    val totalTicks: Long,
) : GameEvent
