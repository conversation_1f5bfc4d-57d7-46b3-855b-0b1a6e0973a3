package com.terheyden.mud.corelib.lock

/**
 * An implementation of Closeable.
 */
open class CloseableImpl(
    private val name: String,
    override var closeable: Boolean = true,
    override var closed: Boolean = false,
) : Closeable {

    init {
        // Do some sanity checks:
        if (!closeable) closed = false
    }

    override fun close() = when {
        closed -> "$name is already closed."
        !closeable -> "$name can't be closed."
        else -> {
            closed = true
            "You close $name."
        }
    }

    override fun open() = when {
        !closed -> "$name is already open."
        !closeable -> "$name can't be opened."
        else -> {
            closed = false
            "You open $name."
        }
    }
}
