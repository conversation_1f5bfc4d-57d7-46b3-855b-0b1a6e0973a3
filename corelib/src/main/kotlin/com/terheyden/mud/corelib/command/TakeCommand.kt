package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.Item
import org.springframework.stereotype.Component

/**
 * Command to take/pick up items from the current room.
 */
@Component
class TakeCommand : Command {
    override val name = "take"
    override val aliases = listOf("get", "pick", "pickup")
    override val description = "Take an item from the current room"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return showTakeableItems()
        }

        val player = GameService.player
        val currentRoom = player.getCurrentRoom()
        val itemName = args.joinToString(" ")

        if (itemName == "all") {
            return CommandResult(takeAll())
        }

        val item = currentRoom.findItem(itemName)

        if (item == null) {
            return CommandResult("There is no '$itemName' here.")
        }

        if (!item.isPickupable) {
            return CommandResult("You can't take the ${item.name}. ${item.getExamineDescription()}")
        }

        if (!player.canAddItem(item)) {
            return CommandResult(
                "You can't carry the ${item.name}. It's too heavy! " +
                        "Your current weight: ${player.weightOfItems}/${player.maxWeight}"
            )
        }

        // Remove from room and add to player inventory
        player.addItem(item)
        return CommandResult("You take the ${item.name}.")
    }

    private fun showTakeableItems(): CommandResult {
        val takeableThings = findTakeableThings()

        if (takeableThings.isEmpty()) {
            return CommandResult("There is nothing here you can take.")
        }

        return CommandResult(
            """
                    You see some nearby things you can take: ${takeableThings.joinToString(", ") { it.name }}

                    Or you could try 'take all'.
                """.trimIndent()
        )
    }

    private fun findTakeableThings(): List<Item> {
        return GameCommands.findNearbyItems(
            includeDoors = false,
            includePlayerInventory = false,
            includePlayerInventoryContainers = false,
        ).filter { it.isPickupable }
    }

    private fun takeAll() = buildString {
        val player = GameService.player
        val takeableThings = findTakeableThings()

        takeableThings.forEach { item ->
            player.addItem(item)
            appendLine("You take: ${item.name}")
        }
    }
}
