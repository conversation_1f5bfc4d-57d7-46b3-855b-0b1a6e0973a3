package com.terheyden.mud.corelib.puzzle

/**
 * Represents the state of a puzzle in the game.
 * Puzzles can be unsolved, partially solved, or completed.
 */
enum class PuzzleState {
    /** The puzzle has not been started or attempted */
    UNSOLVED,

    /** The puzzle has been started but not completed */
    IN_PROGRESS,

    /** The puzzle has been successfully completed */
    SOLVED,

    /** The puzzle has been failed and may need to be reset */
    FAILED
}

/**
 * Interface for objects that can track puzzle progress.
 */
interface PuzzleTracker {
    var puzzleState: PuzzleState

    /**
     * Reset the puzzle to its initial state.
     */
    fun resetPuzzle()

    /**
     * Check if the puzzle is solved.
     */
    fun isSolved(): Boolean = puzzleState == PuzzleState.SOLVED

    /**
     * Mark the puzzle as solved.
     */
    fun solvePuzzle() {
        puzzleState = PuzzleState.SOLVED
    }
}
