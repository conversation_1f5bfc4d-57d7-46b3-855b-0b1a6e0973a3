package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.room.StartingRoom
import org.springframework.stereotype.Component

/**
 * Manages the game world state.
 */
@Component
class GameWorld(
    private val startingRoom: StartingRoom,
) {
    /**
     * Get the starting room ID for new players.
     * This should be configured by the mudlib.
     */
    fun getStartingRoom() = startingRoom.toRoom()
}
