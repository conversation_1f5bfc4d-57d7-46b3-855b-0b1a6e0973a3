package com.terheyden.mud.corelib

/**
 * Base class for all game objects.
 */
open class GameObject(
    override val id: String,
) : Identifiable {
    /** Unique object instance number. */
    override val objectNum = GameService.objects.nextUid()

    /** Unique object ID. */
    override val objectId = "$id-$objectNum"

    init {
        GameService.objects.registerGameObject(this)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as GameObject

        return objectId == other.objectId
    }

    override fun hashCode() = objectId.hashCode()
    override fun toString() = "GameObject(objectId='$objectId')"
}
