package com.terheyden.mud.corelib.lock

import com.terheyden.mud.corelib.Item

interface Lockable {

    /**
     * Is this object lockable?
     * Note that this should just be backed by requiredKeyId != null.
     */
    val lockable: Boolean

    /** Is the object locked? */
    var locked: Boolean

    /**
     * The ID of the key required to unlock this object.
     * If null, the object is not lockable.
     */
    var requiredKeyId: String?

    /**
     * Lock the object with the given key.
     * @return the result message
     */
    fun lock(keyItem: Item): String

    /**
     * Unlock the object with the given key.
     * @return the result message
     */
    fun unlock(keyItem: Item): String
}
