package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.living.Living

/**
 * Base class for objects that can contain other items.
 * Extends Item so containers can themselves be items (like bags, chests, etc.).
 */
open class Container(
    id: String,
    name: String,
    description: String,
    aliases: List<String> = emptyList(),
    weight: Int = 1,
    isPickupable: Boolean = true,
    currentEnv: Container? = null,
    val maxNumItems: Int = Int.MAX_VALUE,
    val maxWeight: Int = Int.MAX_VALUE,
    items: MutableList<Item> = mutableListOf(),
) : Item(
    id = id,
    name = name,
    description = description,
    aliases = aliases,
    weight = weight,
    isPickupable = isPickupable,
    currentEnv = currentEnv,
) {

    /** Contents of this container. */
    protected val _items = items
    val items: List<Item> get() = _items

    /** Total weight of all items in this container (not including the container itself). */
    val weightOfItems: Int get() = _items.sumOf { it.weight }

    /** Total weight of this container and all its contents. */
    val weightTotal: Int get() = weight + weightOfItems

    /**
     * Add an item to this container.
     * @return true if successful, false if container is full or weight limit exceeded
     */
    fun addItem(item: Item): Boolean {
        if (!canAddItem(item)) {
            return false
        }

        item.currentEnv?.removeItem(item)
        _items.add(item)
        return true
    }

    fun canAddItem(item: Item): Boolean {
        return _items.size < maxNumItems && weightOfItems + item.weight <= maxWeight
    }

    /** Remove an item from this container. */
    fun removeItem(itemId: String): Item? {
        return findItem(itemId)?.also { removeItem(it) }
    }

    fun removeItem(item: Item): Boolean {
        return _items.remove(item)
    }

    /** Find an item by ID, name, or alias. */
    fun findItem(itemId: String): Item? {
        return _items.find { it.matches(itemId) }
    }

    /** Find all items that match the given ID, name, or alias. */
    fun findAllItems(itemName: String): List<Item> {
        return _items.filter { it.matches(itemName) }
    }

    /** Find all non-living things within this container. */
    fun findAllNonLiving() = _items.filterNot { it is Living }

    /** Find all containers (not including living things) within this container. */
    fun findAllContainers() = findAllNonLiving()
        .filterIsInstance<Container>()

    /** Find all items within the containers within this container (not including living things). */
    fun findAllItemsInContainers(itemId: String): List<Pair<Container, Item>> {
        return findAllContainers().flatMap { container ->
            container.findAllItems(itemId)
                .map { item -> container to item }
        }
    }

    fun findItemInContainers(itemId: String): Pair<Container, Item>? {
        return findAllItemsInContainers(itemId).firstOrNull()
    }

    /** Check if this container is empty. */
    fun isEmpty(): Boolean = _items.isEmpty()

    /** Get a description of the container's contents. */
    open fun getContentsDescription(): String {
        return _items.joinToString(", ") { it.getShortDescription() }
    }

    override fun toString(): String {
        return "Container(maxNumItems=$maxNumItems, maxWeight=$maxWeight, contents=$items) ${super.toString()}"
    }
}
