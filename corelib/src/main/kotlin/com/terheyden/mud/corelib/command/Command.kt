package com.terheyden.mud.corelib.command

/**
 * Represents a game command that can be executed by the player.
 */
interface Command {
    /**
     * The primary name of the command.
     */
    val name: String

    /**
     * Alternative names/aliases for the command.
     */
    val aliases: List<String>

    /**
     * A brief description of what the command does.
     */
    val description: String

    /**
     * Execute the command with the given arguments.
     * @param args The command arguments (excluding the command name itself)
     * @return The result message to display to the player
     */
    fun execute(args: List<String>): CommandResult
}
