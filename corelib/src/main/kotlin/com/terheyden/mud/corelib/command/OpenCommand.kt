package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Visible
import com.terheyden.mud.corelib.lock.Closeable
import org.springframework.stereotype.Component

/**
 * Command to open doors.
 */
@Component
class OpenCommand : Command {
    override val name = "open"
    override val aliases = emptyList<String>()
    override val description = "Open something like a door or a chest"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            val openableThings = findOpenableThings()

            if (openableThings.isNotEmpty()) {
                return CommandResult("You can open: ${openableThings.joinToString(", ") { it.name }}")
            }

            return CommandResult("You don't see anything here that can be opened.")
        }

        val objName = args.joinToString(" ")

        findOpenableThings().find { it.matches(objName) }?.let {
            return CommandResult((it as Closeable).open())
        }

        return CommandResult("There is no '$objName' here that can be opened.")
    }

    private fun findOpenableThings(): List<Visible> {
        val nearbyItems = GameCommands.findNearbyItems()
        return nearbyItems.filter { it is Closeable }
    }
}
