package com.terheyden.mud.corelib.event

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlin.reflect.KClass

class EventService {

    private val logger = KotlinLogging.logger {}
    private val subscribers = mutableMapOf<KClass<out GameEvent>, MutableList<(GameEvent) -> Unit>>()

    @Suppress("UNCHECKED_CAST")
    fun <T : GameEvent> subscribe(eventClass: KClass<out T>, handler: (T) -> Unit): EventSubscription {
        val castHandler = handler as (GameEvent) -> Unit
        subscribers.getOrPut(eventClass) { mutableListOf() }.add(castHandler)

        return EventSubscription {
            subscribers[eventClass]?.let {
                it.remove(castHandler)
                if (it.isEmpty()) subscribers.remove(eventClass)
            }
        }
    }

    inline fun <reified T : GameEvent> subscribe(noinline handler: (T) -> Unit): EventSubscription {
        return subscribe(T::class, handler)
    }

    fun publish(event: GameEvent) {
        logger.debug { "Publishing event: ${event::class.simpleName}" }
        subscribers[event::class]?.forEach { it(event) }
    }
}
