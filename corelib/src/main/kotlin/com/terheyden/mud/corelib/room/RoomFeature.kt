package com.terheyden.mud.corelib.room

/**
 * Represents an examinable feature within a room.
 * These are environmental elements that players can examine for more detail.
 */
open class RoomFeature(
    val id: String,
    val names: List<String>, // Multiple names/aliases for the feature
    val description: String,
    val keywords: List<String> = emptyList(), // Additional keywords for matching
) {

    /**
     * Get the primary name for this feature.
     */
    val primaryName: String get() = names.first()

    /**
     * Check if the given input matches this feature.
     * Supports partial matching and case-insensitive comparison.
     */
    fun matches(input: String): Boolean {
        val normalizedInput = input.trim().lowercase()

        // Check exact matches first
        if (names.any { it.lowercase() == normalizedInput }) {
            return true
        }

        // Check if input matches any keywords
        if (keywords.any { it.lowercase() == normalizedInput }) {
            return true
        }

        // Check partial matches (input contains any of the names or vice versa)
        return names.any { name ->
            val normalizedName = name.lowercase()
            normalizedInput.contains(normalizedName) || normalizedName.contains(normalizedInput)
        } || keywords.any { keyword ->
            val normalizedKeyword = keyword.lowercase()
            normalizedInput.contains(normalizedKeyword) || normalizedKeyword.contains(normalizedInput)
        }
    }
}
