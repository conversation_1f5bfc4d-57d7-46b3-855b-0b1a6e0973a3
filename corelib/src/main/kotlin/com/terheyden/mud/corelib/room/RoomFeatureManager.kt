package com.terheyden.mud.corelib.room

/**
 * Manages a collection of room features.
 */
class RoomFeatureManager(
    val features: MutableList<RoomFeature> = mutableListOf(),
) : Iterable<RoomFeature> by features {

    /**
     * Find a feature by input string.
     */
    fun findFeature(input: String): RoomFeature? {
        return features.find { it.matches(input) }
    }

    /**
     * Check if there are any features.
     */
    fun isEmpty(): Boolean = features.isEmpty()

    /**
     * Get a list of all examinable things in this room.
     */
    fun getExaminableThings(): List<String> {
        return features.map { it.primaryName }
    }
}
