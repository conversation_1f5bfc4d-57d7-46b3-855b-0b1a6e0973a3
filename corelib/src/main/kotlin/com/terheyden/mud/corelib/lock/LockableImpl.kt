package com.terheyden.mud.corelib.lock

import com.terheyden.mud.corelib.Item

/**
 * A lock that requires a specific key item to unlock.
 */
open class LockableImpl(
    private val name: String,
    override var locked: Boolean = false,
    override var requiredKeyId: String? = null,
) : Lockable {

    init {
        // Do some sanity checks:
        if (requiredKeyId == null) locked = false
    }

    override val lockable: <PERSON><PERSON><PERSON>
        get() = requiredKeyId != null

    override fun lock(keyItem: Item) = when {
        locked -> "$name is already locked."
        !lockable -> "$name can't be locked."
        requiredKeyId != keyItem.id -> "$name can't be locked with that key."
        else -> {
            locked = true
            "You lock $name."
        }
    }

    override fun unlock(keyItem: Item) = when {
        !locked -> "$name is already unlocked."
        !lockable -> "$name can't be unlocked."
        requiredKeyId != keyItem.id -> "$name can't be unlocked with that key."
        else -> {
            locked = false
            "You unlock $name."
        }
    }
}
