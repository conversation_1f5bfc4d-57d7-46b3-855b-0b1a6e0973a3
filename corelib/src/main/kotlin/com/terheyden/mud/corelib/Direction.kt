package com.terheyden.mud.corelib

/**
 * Represents the cardinal directions for movement.
 */
enum class Direction(val aliases: List<String>) {
    NORTH(listOf("north", "n")),
    SOUTH(listOf("south", "s")),
    EAST(listOf("east", "e")),
    WEST(listOf("west", "w")),
    NORTHEAST(listOf("northeast", "ne")),
    NORTHWEST(listOf("northwest", "nw")),
    SOUTHEAST(listOf("southeast", "se")),
    SOUTHWEST(listOf("southwest", "sw")),
    UP(listOf("up", "u")),
    DOWN(listOf("down", "d"));

    val lowercaseName = name.lowercase()

    companion object {
        /**
         * Parse a direction from user input.
         */
        fun fromString(input: String): Direction? {
            val normalized = input.trim().lowercase()
            return entries.find { direction ->
                direction.aliases.any { it == normalized }
            }
        }
    }
}
