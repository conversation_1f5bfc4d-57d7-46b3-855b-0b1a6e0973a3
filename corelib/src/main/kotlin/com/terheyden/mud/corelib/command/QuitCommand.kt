package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to quit the game.
 */
@Component
class QuitCommand : Command {
    override val name = "quit"
    override val aliases = listOf("exit", "bye", "q")
    override val description = "Exit the game"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        return CommandResult(
            message = "Thanks for playing! Goodbye, ${player.name}!",
            shouldQuit = true
        )
    }
}
