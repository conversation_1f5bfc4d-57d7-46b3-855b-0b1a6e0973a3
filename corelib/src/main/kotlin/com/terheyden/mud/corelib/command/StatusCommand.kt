package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to show the player's current status including health, level, and equipment.
 */
@Component
class StatusCommand : Command {
    override val name = "status"
    override val aliases = listOf("stats", "st", "health", "hp")
    override val description = "Show your current health, level, and combat statistics"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        return CommandResult(player.getCombatStatus())
    }
}
