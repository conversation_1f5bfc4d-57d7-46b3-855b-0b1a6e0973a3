package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.lock.CloseAndLockable

/**
 * A container that can be closed and locked.
 */
open class CloseLockContainer(
    id: String,
    name: String,
    description: String,
    aliases: List<String> = emptyList(),
    weight: Int = 1,
    isPickupable: Boolean = true,
    maxNumItems: Int = Int.MAX_VALUE,
    maxWeight: Int = Int.MAX_VALUE,
    items: MutableList<Item> = mutableListOf(),
    override var closeable: Boolean = true,
    override var closed: Boolean = false,
    override var locked: Boolean = false,
    override var requiredKeyId: String? = null,
) : Container(
    id = id,
    name = name,
    description = description,
    aliases = aliases,
    weight = weight,
    isPickupable = isPickupable,
    maxNumItems = maxNumItems,
    maxWeight = maxWeight,
    items = items,
), CloseAndLockable {

    init {
        // Do some close lock state sanity checks:
        if (!lockable) locked = false
        if (!closeable) closed = false
        if (locked) closed = true
        if (requiredKeyId == null) locked = false
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()

            if (closed) {
                appendLine("The $name is closed.")
            } else {
                appendLine("The $name is open. It contains: " + getContentsDescription())
            }
        }
    }

    override fun getContentsDescription(): String {
        return if (closed) {
            "$name is closed - you cannot see its contents."
        } else {
            super.getContentsDescription()
        }
    }

    // CLOSE LOCKABLE:

    override fun close() = when {
        locked -> "$name is locked and can't be closed."
        closed -> "$name is already closed."
        !closeable -> "$name can't be closed."
        else -> {
            closed = true
            "You close $name."
        }
    }

    override fun open() = when {
        locked -> "$name is locked and can't be opened."
        !closed -> "$name is already open."
        !closeable -> "$name can't be opened."
        else -> {
            closed = false
            "You open $name."
        }
    }

    override val lockable: Boolean
        get() = requiredKeyId != null

    override fun lock(keyItem: Item) = when {
        !closed -> "$name is open and can't be locked."
        locked -> "$name is already locked."
        !lockable -> "$name can't be locked."
        requiredKeyId != keyItem.id -> "$name can't be locked with that key."
        else -> {
            locked = true
            "You lock $name."
        }
    }

    override fun unlock(keyItem: Item) = when {
        !closed -> "$name is open and can't be unlocked."
        !locked -> "$name is already unlocked."
        !lockable -> "$name can't be unlocked."
        requiredKeyId != keyItem.id -> "$name can't be unlocked with that key."
        else -> {
            locked = false
            "You unlock $name."
        }
    }
}
