package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.GameService.rooms
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.Room

/**
 * Actions that can take place in the game world.
 * Moving things, saying things, etc.
 */
object GameCommands {

    fun move(player: Player, direction: Direction): String {
        val currentRoom = player.getCurrentRoom()

        // Check if movement is blocked.
        currentRoom.isMovementBlocked(direction)?.let { blockMsg ->
            return blockMsg
        }

        // Use the room registry to resolve the exit (handles both room IDs and room classes)
        val destinationRoom = rooms.resolveExit(currentRoom, direction)
        if (destinationRoom == null) {
            return "You can't go ${direction.lowercaseName} from here."
        }

        // Move the player
        player.moveTo(destinationRoom)

        // Handle NPC reactions to player entering
        val npcReactions = destinationRoom.handlePlayerEnter(player)

        return buildString {
            appendLine("You go ${direction.lowercaseName}.")
            appendLine()
            append(look(destinationRoom, npcReactions))
        }
    }

    fun look(
        room: Room,
        onEnterRoomMessages: String? = null,
    ) = buildString {
        append(room.getFullDescription())

        // Add NPC reactions if any
        if (onEnterRoomMessages != null) {
            appendLine()
            appendLine(onEnterRoomMessages)
        }

        // Show any recent room messages (NPC speech, actions, etc.)
        val roomMessages = room.getAndClearRoomMessages()
        if (roomMessages.isNotEmpty()) {
            appendLine()
            appendLine("*Recent activity:*")
            roomMessages.forEach { roomMessage ->
                appendLine("  $roomMessage")
            }
        }
    }

    fun findNearbyItems(
        includeDoors: Boolean = true,
        includePlayerInventory: Boolean = true,
        includeRoomContents: Boolean = true,
        includeRoomContainers: Boolean = true,
        includePlayerInventoryContainers: Boolean = true,
    ): Set<Item> {
        val foundItems = mutableSetOf<Item>()
        val player = GameService.player
        val currentRoom = player.getCurrentRoom()

        if (includeDoors) {
            foundItems.addAll(currentRoom.doors.values)
        }

        if (includeRoomContents) {
            foundItems.addAll(currentRoom.items)
        }

        if (includeRoomContainers) {
            currentRoom.findAllContainers().forEach { container ->
                foundItems.addAll(container.items)
            }
        }

        if (includePlayerInventory) {
            foundItems.addAll(player.items)
        }

        if (includePlayerInventoryContainers) {
            player.findAllContainers().forEach { container ->
                foundItems.addAll(container.items)
            }
        }

        return foundItems
    }
}
