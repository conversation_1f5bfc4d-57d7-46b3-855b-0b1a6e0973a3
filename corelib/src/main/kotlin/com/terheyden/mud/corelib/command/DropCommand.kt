package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to drop items from inventory to the current room.
 */
@Component
class DropCommand : Command {
    override val name = "drop"
    override val aliases = listOf("put", "place")
    override val description = "Drop an item from your inventory"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Drop what? Specify an item to drop.")
        }

        val player = GameService.player
        val currentRoom = player.getCurrentRoom()
        val itemName = args.joinToString(" ")
        val item = player.removeItem(itemName)

        if (item == null) {
            return CommandResult("You don't have '$itemName' in your inventory.")
        }

        // Add to current room
        currentRoom.addItem(item)

        return CommandResult("You drop the ${item.name}.")
    }
}
