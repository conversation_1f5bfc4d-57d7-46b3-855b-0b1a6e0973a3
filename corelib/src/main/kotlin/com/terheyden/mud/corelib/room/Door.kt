package com.terheyden.mud.corelib.room

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.GameService.events
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.event.GameEvent
import com.terheyden.mud.corelib.lock.CloseAndLockable

/**
 * Represents a door that can be opened, closed, locked, and unlocked.
 * Doors can block movement between rooms when closed or locked.
 */
open class Door(
    id: String,
    name: String,
    description: String,
    aliases: List<String> = emptyList(),
    val direction: Direction,
    override var closeable: Boolean = true,
    override var closed: Boolean = false,
    override var locked: Boolean = false,
    override var requiredKeyId: String? = null,
) : Item(
    id = id,
    name = name,
    description = description,
    aliases = aliases,
), CloseAndLockable {

    init {
        // Do some close lock state sanity checks:
        if (!lockable) locked = false
        if (!closeable) closed = false
        if (locked) closed = true
        if (requiredKeyId == null) locked = false

        // Sync all instances so when they're closed / locked on the other side,
        // then this side is also closed / locked.
        events.subscribe<DoorChangedEvent> { event ->
            if (event.doorId == id) {
                this.closed = event.closed
                this.locked = event.locked
            }
        }
    }

    fun getExitVerb() = when {
        locked -> "locked"
        closed -> "closed"
        else -> "open"
    }

    fun getExitDescription() = "$name leading ${direction.lowercaseName} (${getExitVerb()})"

    // CLOSE LOCKABLE:

    override fun close() = when {
        locked -> "$name is locked and can't be closed."
        closed -> "$name is already closed."
        !closeable -> "$name can't be closed."
        else -> {
            closed = true
            events.publish(DoorChangedEvent(id, closed, locked))
            "You close $name."
        }
    }

    override fun open() = when {
        locked -> "$name is locked and can't be opened."
        !closed -> "$name is already open."
        !closeable -> "$name can't be opened."
        else -> {
            closed = false
            events.publish(DoorChangedEvent(id, closed, locked))
            "You open $name."
        }
    }

    override val lockable: Boolean
        get() = requiredKeyId != null

    override fun lock(keyItem: Item) = when {
        !closed -> "$name is open and can't be locked."
        locked -> "$name is already locked."
        !lockable -> "$name can't be locked."
        requiredKeyId != keyItem.id -> "$name can't be locked with that key."
        else -> {
            locked = true
            events.publish(DoorChangedEvent(id, closed, locked))
            "You lock $name."
        }
    }

    override fun unlock(keyItem: Item) = when {
        !closed -> "$name is open and can't be unlocked."
        !locked -> "$name is already unlocked."
        !lockable -> "$name can't be unlocked."
        requiredKeyId != keyItem.id -> "$name can't be unlocked with that key."
        else -> {
            locked = false
            events.publish(DoorChangedEvent(id, closed, locked))
            "You unlock $name."
        }
    }

    data class DoorChangedEvent(
        val doorId: String,
        val closed: Boolean,
        val locked: Boolean,
    ) : GameEvent
}
