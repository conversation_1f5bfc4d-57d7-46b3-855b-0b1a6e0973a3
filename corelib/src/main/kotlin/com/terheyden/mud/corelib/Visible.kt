package com.terheyden.mud.corelib

/**
 * Visibles are things the player can see, but not necessarily separate items.
 * For example, a room feature like a fountain or a painting, or a door lock.
 */
interface Visible {
    var name: String
    var description: String
    var aliases: List<String>

    /**
     * Check if this item matches the given input string.
     * Matches against the item's name and aliases (case-insensitive).
     */
    fun matches(input: String): <PERSON><PERSON><PERSON> {
        val normalizedInput = input.trim().lowercase()

        // Check exact matches first
        if (name.lowercase() == normalizedInput) {
            return true
        }

        // Check aliases
        if (aliases.any { it.lowercase() == normalizedInput }) {
            return true
        }

        // Check if input is contained in name or vice versa (for partial matching)
        val normalizedName = name.lowercase()
        return normalizedInput.contains(normalizedName) || normalizedName.contains(normalizedInput)
    }
}
