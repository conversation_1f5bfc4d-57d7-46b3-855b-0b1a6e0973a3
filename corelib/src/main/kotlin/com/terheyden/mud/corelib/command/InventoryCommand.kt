package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to show the player's inventory.
 */
@Component
class InventoryCommand : Command {
    override val name = "inventory"
    override val aliases = listOf("inv", "i")
    override val description = "Show your inventory"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        return CommandResult(player.getInventoryDescription())
    }
}
