plugins {
    kotlin("jvm")

    // For importing JARs and docs into the IDE.
    idea
}

group = "com.terheyden"
version = "0.0.1-SNAPSHOT"

// Apply a specific Java toolchain to ease working on different environments.
java {
    toolchain {
        // Create JARs with sources and javadocs attached.
        withJavadocJar()
        withSourcesJar()

        // Set JDK version:
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring dependencies for components
    // https://mvnrepository.com/artifact/org.springframework/spring-context
    implementation("org.springframework:spring-context:6.2.9")
    implementation("org.jetbrains.kotlin:kotlin-reflect")

    // LOGGING

    // Kotlin logging API.
    // https://github.com/oshai/kotlin-logging
    // https://mvnrepository.com/artifact/io.github.oshai/kotlin-logging-jvm
    implementation("io.github.oshai:kotlin-logging-jvm:7.0.5")
    // kotlin-logging requires SLF4J API:
    // https://mvnrepository.com/artifact/org.slf4j/slf4j-api
    implementation("org.slf4j:slf4j-api:2.0.17")
    // SLF4J requires an implementation -- we'll use Log4J2:
    // https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-slf4j2-impl
    testImplementation("org.apache.logging.log4j:log4j-slf4j2-impl:2.24.3")

    // TESTING:

    // Use the Kotlin JUnit 5 integration.
    // https://mvnrepository.com/artifact/org.jetbrains.kotlin/kotlin-test-junit5
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5:2.0.21")
    // Use the JUnit 5 integration.
    // https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-engine
    val junitVersion = "5.13.2"
    testImplementation("org.junit.jupiter:junit-jupiter-engine:$junitVersion")
    testImplementation("org.junit.jupiter:junit-jupiter-params:$junitVersion")
    // https://mvnrepository.com/artifact/org.junit.platform/junit-platform-launcher
    testRuntimeOnly("org.junit.platform:junit-platform-launcher:1.13.2")
    // Fluent assertions:
    // https://mvnrepository.com/artifact/org.assertj/assertj-core
    testImplementation("org.assertj:assertj-core:3.27.3")
    // https://mvnrepository.com/artifact/io.mockk/mockk
    testImplementation("io.mockk:mockk:1.14.3")
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

// Download sources and docs for dependencies in IntelliJ IDEA.
idea {
    module {
        isDownloadJavadoc = true
        isDownloadSources = true
    }
}
