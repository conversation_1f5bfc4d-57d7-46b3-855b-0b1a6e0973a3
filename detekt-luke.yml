# To generate the default config, run: ./gradlew detektGenerateConfig
# See: https://detekt.dev/docs/intro
#
# For rule sets, see: https://detekt.dev/docs/rules/comments

config:
  validation: true
  warningsAsErrors: false
  checkExhaustiveness: false

processors:
  active: true
  exclude:
    - 'DetektProgressListener'
    - 'KtFileCountProcessor'
    - 'PackageCountProcessor'
    - 'ClassCountProcessor'
    - 'FunctionCountProcessor'
    - 'PropertyCountProcessor'

complexity:
  LongParameterList:
    active: false
  TooManyFunctions:
    active: false

exceptions:
  # Oftentimes it makes sense to catch all exceptions.
  TooGenericExceptionCaught:
    active: false

style:
  ReturnCount:
    active: false

